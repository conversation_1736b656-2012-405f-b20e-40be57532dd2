### Get all service requests (admin)
GET http://localhost:3000/api/service-requests
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE

### Get all service requests with filtering
GET http://localhost:3000/api/service-requests?status=PENDING&priority=HIGH&page=1&limit=10
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE

### Get a specific service request
GET http://localhost:3000/api/service-requests/SERVICE_REQUEST_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

### Create a new service request (resident)
POST http://localhost:3000/api/service-requests
Content-Type: application/json
Authorization: Bearer YOUR_RESIDENT_TOKEN_HERE

{
  "title": "Air conditioner not working",
  "description": "The air conditioner in my room is making strange noises and not cooling properly.",
  "type": "APPLIANCE",
  "priority": "MEDIUM",
  "roomId": "ROOM_ID_HERE",
  "images": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ]
}

### Update a service request status (admin only)
PUT http://localhost:3000/api/service-requests/SERVICE_REQUEST_ID_HERE
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE

{
  "status": "IN_PROGRESS",
  "priority": "HIGH",
  "assignedTo": "ADMIN_ID_HERE"
}

### Delete a service request
DELETE http://localhost:3000/api/service-requests/SERVICE_REQUEST_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

### Add a response to a service request (admin)
POST http://localhost:3000/api/service-requests/SERVICE_REQUEST_ID_HERE/respond
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE

{
  "message": "We have scheduled a technician to check your air conditioner tomorrow between 10 AM and 12 PM.",
  "attachments": "https://example.com/schedule.pdf",
  "updateStatus": "IN_PROGRESS"
}

### Add a response to a service request (resident)
POST http://localhost:3000/api/service-requests/SERVICE_REQUEST_ID_HERE/respond
Content-Type: application/json
Authorization: Bearer YOUR_RESIDENT_TOKEN_HERE

{
  "message": "Thank you for the quick response. I'll be available during that time."
}

### Get service requests for a specific user
GET http://localhost:3000/api/service-requests/user/USER_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

### Get service requests for a specific user with filtering
GET http://localhost:3000/api/service-requests/user/USER_ID_HERE?status=PENDING&page=1&limit=10
Authorization: Bearer YOUR_TOKEN_HERE
