// Script to seed payment data for testing
const { PrismaClient } = require('../prisma/generated/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting payment seeding...');

    // Get all users with RESIDENT role
    const residents = await prisma.user.findMany({
      where: {
        role: 'RESIDENT',
      },
    });

    if (residents.length === 0) {
      console.log('No residents found. Please create residents first.');
      return;
    }

    console.log(`Found ${residents.length} residents`);

    // Get all rooms
    const rooms = await prisma.room.findMany({
      include: {
        roomType: true,
      },
    });

    if (rooms.length === 0) {
      console.log('No rooms found. Please create rooms first.');
      return;
    }

    console.log(`Found ${rooms.length} rooms`);

    // Create sample payments for each resident
    const payments = [];
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    for (const resident of residents) {
      // Find the room for this resident
      const residentRoom = await prisma.room.findFirst({
        where: {
          residents: {
            some: {
              id: resident.id,
            },
          },
        },
        include: {
          roomType: true,
        },
      });

      if (!residentRoom) {
        console.log(`No room found for resident ${resident.name}. Skipping.`);
        continue;
      }

      console.log(`Creating payments for resident ${resident.name} in room ${residentRoom.roomNumber}`);

      // Create rent payment for current month (paid)
      payments.push({
        userId: resident.id,
        roomId: residentRoom.id,
        amount: residentRoom.monthlyRate,
        dueDate: new Date(currentYear, currentMonth, 15),
        paidDate: new Date(currentYear, currentMonth, 10),
        status: 'PAID',
        type: 'RENT',
        notes: `Monthly rent payment for ${new Date(currentYear, currentMonth).toLocaleString('default', { month: 'long', year: 'numeric' })}`,
        receiptNumber: `RCT-${currentYear}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
        paymentMethod: 'Bank Transfer',
      });

      // Create utilities payment for current month (pending)
      payments.push({
        userId: resident.id,
        roomId: residentRoom.id,
        amount: Math.floor(Math.random() * 1000) + 500, // Random amount between 500-1500
        dueDate: new Date(currentYear, currentMonth, 20),
        status: 'PENDING',
        type: 'UTILITIES',
        notes: `Utilities payment for ${new Date(currentYear, currentMonth).toLocaleString('default', { month: 'long', year: 'numeric' })}`,
      });

      // Create rent payment for next month (upcoming)
      payments.push({
        userId: resident.id,
        roomId: residentRoom.id,
        amount: residentRoom.monthlyRate,
        dueDate: new Date(currentYear, currentMonth + 1, 15),
        status: 'UPCOMING',
        type: 'RENT',
        notes: `Monthly rent payment for ${new Date(currentYear, currentMonth + 1).toLocaleString('default', { month: 'long', year: 'numeric' })}`,
      });

      // Create a past due payment (overdue)
      payments.push({
        userId: resident.id,
        roomId: residentRoom.id,
        amount: 300,
        dueDate: new Date(currentYear, currentMonth - 1, 25),
        status: 'OVERDUE',
        type: 'MAINTENANCE',
        notes: 'Maintenance fee for plumbing repairs',
      });
    }

    // Create the payments in the database
    console.log(`Creating ${payments.length} payment records...`);
    
    // Delete existing payments first
    await prisma.payment.deleteMany({});
    console.log('Deleted existing payments');
    
    // Create new payments
    for (const payment of payments) {
      await prisma.payment.create({
        data: payment,
      });
    }

    console.log('Payment seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding payments:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
