"use client";

import { useState, useEffect } from 'react';
import Modal from '@/components/admin/Modal';
import <PERSON>Field from '@/components/admin/FormField';

interface AddRoomModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddRoom: (room: any) => void;
  roomTypeId: number;
  roomTypeName: string;
  defaultMonthlyRate: number;
}

export default function AddRoomModal({
  isOpen,
  onClose,
  onAddRoom,
  roomTypeId,
  roomTypeName,
  defaultMonthlyRate
}: AddRoomModalProps) {
  const [formData, setFormData] = useState({
    roomNumber: '',
    monthlyRate: defaultMonthlyRate,
    isOccupied: false
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Reset form when modal opens with new room type
  useEffect(() => {
    setFormData({
      roomNumber: '',
      monthlyRate: defaultMonthlyRate,
      isOccupied: false
    });
  }, [defaultMonthlyRate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    // Handle different input types
    const newValue = type === 'checkbox'
      ? (e.target as HTMLInputElement).checked
      : type === 'number'
        ? Number(value)
        : value;

    setFormData({
      ...formData,
      [name]: newValue
    });

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.roomNumber.trim()) {
      errors.roomNumber = 'Room number is required';
    }

    if (formData.monthlyRate <= 0) {
      errors.monthlyRate = 'Monthly rate must be greater than 0';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddRoom = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    onAddRoom({
      ...formData,
      roomTypeId,
      id: `${roomTypeId}-${Date.now()}` // Generate a unique ID
    });

    // Reset form
    setFormData({
      roomNumber: '',
      monthlyRate: defaultMonthlyRate,
      isOccupied: false
    });
    setFormErrors({});
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Add Room to ${roomTypeName}`}
      footer={
        <>
          <button
            type="button"
            onClick={handleAddRoom}
            className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-amber-500 text-base font-medium text-white hover:bg-amber-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Add Room
          </button>
          <button
            type="button"
            onClick={onClose}
            className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Cancel
          </button>
        </>
      }
    >
      <form onSubmit={handleAddRoom}>
        <div className="space-y-4">
          <FormField
            label="Room Number"
            name="roomNumber"
            value={formData.roomNumber}
            onChange={handleInputChange}
            placeholder="e.g., A-101"
            required
            error={formErrors.roomNumber}
          />
          <FormField
            label="Monthly Rate (฿)"
            name="monthlyRate"
            type="number"
            value={formData.monthlyRate}
            onChange={handleInputChange}
            required
            error={formErrors.monthlyRate}
          />
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isOccupied"
              name="isOccupied"
              checked={formData.isOccupied}
              onChange={handleInputChange}
              className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
            />
            <label htmlFor="isOccupied" className="ml-2 block text-sm text-gray-700">
              Room is currently occupied
            </label>
          </div>
        </div>
      </form>
    </Modal>
  );
}
