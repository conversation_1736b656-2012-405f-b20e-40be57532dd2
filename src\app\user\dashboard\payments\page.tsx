"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';

interface Payment {
  id: string;
  date: Date;
  paidDate: Date | null;
  amount: number;
  status: 'PAID' | 'PENDING' | 'UPCOMING' | 'OVERDUE';
  type: 'RENT' | 'UTILITIES' | 'DEPOSIT' | 'MAINTENANCE' | 'OTHER';
  receiptNumber?: string;
  paymentMethod?: string;
  notes?: string;
  room?: {
    roomNumber: string;
    roomType: string;
  } | null;
}

interface PaymentSummary {
  paid: { count: number; total: number };
  pending: { count: number; total: number };
  upcoming: { count: number; total: number };
  overdue: { count: number; total: number };
}

interface PaymentData {
  payments: Payment[];
  summary: PaymentSummary;
  nextPayment: {
    id: string;
    date: Date;
    amount: number;
    type: string;
  } | null;
}

export default function PaymentsPage() {
  const { user } = useAuth();
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPaymentData = async () => {
      if (!user) return;

      try {
        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication token not found. Please log in again.');
          setIsLoading(false);
          return;
        }

        const response = await fetch('/api/dashboard/payments', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch payment data: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
          setPaymentData(result.data);
        } else {
          setError(result.message || 'Failed to fetch payment data');
        }
      } catch (err) {
        console.error('Error fetching payment data:', err);
        setError('An error occurred while fetching payment data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPaymentData();
  }, [user]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
        <p>{error}</p>
      </div>
    );
  }

  // No data state
  if (!paymentData) {
    return (
      <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
        <p>No payment data available. Please try again later.</p>
      </div>
    );
  }

  // Format payments for the PaymentSchedule component
  const formattedPayments = paymentData.payments.map(payment => ({
    id: payment.id,
    date: new Date(payment.date).toISOString().split('T')[0],
    amount: payment.amount,
    status: payment.status === 'PAID' ? 'paid' :
            payment.status === 'PENDING' ? 'pending' :
            payment.status === 'UPCOMING' ? 'upcoming' :
            payment.status === 'OVERDUE' ? 'overdue' : 'pending',
    type: payment.type === 'RENT' ? 'Rent' :
          payment.type === 'UTILITIES' ? 'Utilities' :
          payment.type === 'DEPOSIT' ? 'Deposit' :
          payment.type === 'MAINTENANCE' ? 'Maintenance' : 'Other'
  }));

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="space-y-6">
        {/* Payment Summary */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold mb-4">Payment Summary</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <p className="text-sm text-green-600 font-medium">Paid</p>
              <p className="text-2xl font-bold text-green-700">฿{paymentData.summary.paid.total.toLocaleString()}</p>
              <p className="text-xs text-green-600">{paymentData.summary.paid.count} payments</p>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg">
              <p className="text-sm text-amber-600 font-medium">Pending</p>
              <p className="text-2xl font-bold text-amber-700">฿{paymentData.summary.pending.total.toLocaleString()}</p>
              <p className="text-xs text-amber-600">{paymentData.summary.pending.count} payments</p>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <p className="text-sm text-red-600 font-medium">Overdue</p>
              <p className="text-2xl font-bold text-red-700">฿{paymentData.summary.overdue.total.toLocaleString()}</p>
              <p className="text-xs text-red-600">{paymentData.summary.overdue.count} payments</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600 font-medium">Upcoming</p>
              <p className="text-2xl font-bold text-gray-700">฿{paymentData.summary.upcoming.total.toLocaleString()}</p>
              <p className="text-xs text-gray-600">{paymentData.summary.upcoming.count} payments</p>
            </div>
          </div>
        </div>



        {/* Payment History */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold mb-4">Payment History</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paymentData.payments.map((payment) => (
                  <tr key={payment.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {new Date(payment.date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.type === 'RENT' ? 'Rent' :
                       payment.type === 'UTILITIES' ? 'Utilities' :
                       payment.type === 'DEPOSIT' ? 'Deposit' :
                       payment.type === 'MAINTENANCE' ? 'Maintenance' : 'Other'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">฿{payment.amount.toLocaleString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        payment.status === 'PAID' ? 'bg-green-100 text-green-800' :
                        payment.status === 'PENDING' ? 'bg-amber-100 text-amber-800' :
                        payment.status === 'OVERDUE' ? 'bg-red-100 text-red-800' :
                        payment.status === 'UPCOMING' ? 'bg-gray-100 text-gray-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {payment.status === 'PAID' ? 'Paid' :
                         payment.status === 'PENDING' ? 'Pending' :
                         payment.status === 'OVERDUE' ? 'Overdue' :
                         payment.status === 'UPCOMING' ? 'Upcoming' :
                         payment.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {payment.status === 'PENDING' && (
                        <a href={`/payment/${payment.id}`} className="text-amber-600 hover:text-amber-800 font-medium">Pay Now</a>
                      )}
                      {payment.status === 'PAID' && payment.receiptNumber && (
                        <button className="text-blue-600 hover:text-blue-800 font-medium">View Receipt</button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
