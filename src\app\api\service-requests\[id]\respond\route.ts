import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// POST add a response to a service request
async function handlePost(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { message, attachments, updateStatus } = body;
    
    if (!message || message.trim() === '') {
      return NextResponse.json(
        { success: false, message: 'Response message is required' },
        { status: 400 }
      );
    }

    // Check if service request exists
    const serviceRequest = await prisma.serviceRequest.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!serviceRequest) {
      return NextResponse.json(
        { success: false, message: 'Service request not found' },
        { status: 404 }
      );
    }

    // Find an admin user to associate with the response
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    // Create the response
    let responseData = {
      message,
      attachments,
      serviceRequestId: id,
      userId: adminUser?.id // Use admin user ID if found
    };

    // Create the response
    const response = await prisma.serviceResponse.create({
      data: responseData
    });

    // Update service request status if requested
    if (updateStatus) {
      await prisma.serviceRequest.update({
        where: { id },
        data: {
          status: updateStatus,
          resolvedAt: updateStatus === 'RESOLVED' ? new Date() : undefined
        }
      });
    }

    // Create a notification for the resident
    await prisma.notification.create({
      data: {
        userId: serviceRequest.userId,
        title: 'New Response to Your Service Request',
        message: `An administrator has responded to your service request "${serviceRequest.title}".`,
        type: 'SERVICE_REQUEST_UPDATE',
        link: `/resident/service-requests/${id}`
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Response added successfully',
      data: response
    });
  } catch (error) {
    console.error(`Error adding response to service request with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to add response' },
      { status: 500 }
    );
  }
}

// Export the POST handler directly without authentication middleware
export { handlePost as POST };
