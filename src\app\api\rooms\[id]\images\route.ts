import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// POST add an image to a room
async function handlePost(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { url } = body;

    // Validate required fields
    if (!url) {
      return NextResponse.json(
        { success: false, message: 'Image URL is required' },
        { status: 400 }
      );
    }

    // Check if room exists
    const room = await prisma.room.findUnique({
      where: { id }
    });

    if (!room) {
      return NextResponse.json(
        { success: false, message: 'Room not found' },
        { status: 404 }
      );
    }

    // Add the image
    const image = await prisma.roomImage.create({
      data: {
        url,
        roomId: id
      }
    });

    return NextResponse.json(
      {
        success: true,
        message: 'Image added successfully',
        data: image
      },
      { status: 201 }
    );
  } catch (error) {
    console.error(`Error adding image to room with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to add image' },
      { status: 500 }
    );
  }
}

// GET all images for a room
async function handleGet(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Check if room exists
    const room = await prisma.room.findUnique({
      where: { id }
    });

    if (!room) {
      return NextResponse.json(
        { success: false, message: 'Room not found' },
        { status: 404 }
      );
    }

    // Get all images for the room
    const images = await prisma.roomImage.findMany({
      where: { roomId: id },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({
      success: true,
      data: images
    });
  } catch (error) {
    console.error(`Error fetching images for room with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch images' },
      { status: 500 }
    );
  }
}

// Export the handlers with authentication middleware
export const GET = handleGet;
export const POST = withAuth(handlePost, ['ADMIN']);
