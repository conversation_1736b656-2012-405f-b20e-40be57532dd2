import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

// GET payment statistics
async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Get current date
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    
    // Start of current month
    const startOfMonth = new Date(currentYear, currentMonth, 1);
    
    // Start of next month
    const startOfNextMonth = new Date(currentYear, currentMonth + 1, 1);
    
    // Start of previous month
    const startOfPrevMonth = new Date(currentYear, currentMonth - 1, 1);

    // Get all payments
    const allPayments = await prisma.payment.findMany();
    
    // Get payments for current month
    const currentMonthPayments = allPayments.filter(payment => {
      const paymentDate = new Date(payment.dueDate);
      return paymentDate >= startOfMonth && paymentDate < startOfNextMonth;
    });
    
    // Get payments for previous month
    const prevMonthPayments = allPayments.filter(payment => {
      const paymentDate = new Date(payment.dueDate);
      return paymentDate >= startOfPrevMonth && paymentDate < startOfMonth;
    });

    // Calculate statistics
    const totalPayments = allPayments.length;
    const paidPayments = allPayments.filter(p => p.status === 'PAID').length;
    const pendingPayments = allPayments.filter(p => p.status === 'PENDING').length;
    const overduePayments = allPayments.filter(p => p.status === 'OVERDUE').length;
    const upcomingPayments = allPayments.filter(p => p.status === 'UPCOMING').length;
    
    const totalAmount = allPayments.reduce((sum, p) => sum + p.amount, 0);
    const paidAmount = allPayments.filter(p => p.status === 'PAID').reduce((sum, p) => sum + p.amount, 0);
    const pendingAmount = allPayments.filter(p => p.status === 'PENDING').reduce((sum, p) => sum + p.amount, 0);
    const overdueAmount = allPayments.filter(p => p.status === 'OVERDUE').reduce((sum, p) => sum + p.amount, 0);
    
    // Current month statistics
    const currentMonthTotal = currentMonthPayments.reduce((sum, p) => sum + p.amount, 0);
    const currentMonthPaid = currentMonthPayments.filter(p => p.status === 'PAID').reduce((sum, p) => sum + p.amount, 0);
    const currentMonthPending = currentMonthPayments.filter(p => p.status === 'PENDING').reduce((sum, p) => sum + p.amount, 0);
    const currentMonthOverdue = currentMonthPayments.filter(p => p.status === 'OVERDUE').reduce((sum, p) => sum + p.amount, 0);
    
    // Previous month statistics
    const prevMonthTotal = prevMonthPayments.reduce((sum, p) => sum + p.amount, 0);
    const prevMonthPaid = prevMonthPayments.filter(p => p.status === 'PAID').reduce((sum, p) => sum + p.amount, 0);
    
    // Payment types breakdown
    const rentPayments = allPayments.filter(p => p.type === 'RENT').length;
    const utilitiesPayments = allPayments.filter(p => p.type === 'UTILITIES').length;
    const depositPayments = allPayments.filter(p => p.type === 'DEPOSIT').length;
    const maintenancePayments = allPayments.filter(p => p.type === 'MAINTENANCE').length;
    const otherPayments = allPayments.filter(p => p.type === 'OTHER').length;
    
    // Monthly trend (last 6 months)
    const monthlyTrend = [];
    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date(currentYear, currentMonth - i, 1);
      const monthName = monthDate.toLocaleString('default', { month: 'short' });
      const nextMonth = new Date(currentYear, currentMonth - i + 1, 1);
      
      const monthPayments = allPayments.filter(payment => {
        const paymentDate = new Date(payment.dueDate);
        return paymentDate >= monthDate && paymentDate < nextMonth;
      });
      
      const monthTotal = monthPayments.reduce((sum, p) => sum + p.amount, 0);
      const monthPaid = monthPayments.filter(p => p.status === 'PAID').reduce((sum, p) => sum + p.amount, 0);
      
      monthlyTrend.push({
        month: monthName,
        total: monthTotal,
        paid: monthPaid,
        pending: monthTotal - monthPaid,
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          totalPayments,
          paidPayments,
          pendingPayments,
          overduePayments,
          upcomingPayments,
          totalAmount,
          paidAmount,
          pendingAmount,
          overdueAmount,
          collectionRate: totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0,
        },
        currentMonth: {
          name: new Date().toLocaleString('default', { month: 'long' }),
          total: currentMonthTotal,
          paid: currentMonthPaid,
          pending: currentMonthPending,
          overdue: currentMonthOverdue,
          collectionRate: currentMonthTotal > 0 ? (currentMonthPaid / currentMonthTotal) * 100 : 0,
        },
        previousMonth: {
          name: new Date(currentYear, currentMonth - 1).toLocaleString('default', { month: 'long' }),
          total: prevMonthTotal,
          paid: prevMonthPaid,
          collectionRate: prevMonthTotal > 0 ? (prevMonthPaid / prevMonthTotal) * 100 : 0,
        },
        paymentTypes: {
          rent: rentPayments,
          utilities: utilitiesPayments,
          deposit: depositPayments,
          maintenance: maintenancePayments,
          other: otherPayments,
        },
        monthlyTrend,
      },
    });
  } catch (error) {
    console.error('Error fetching payment statistics:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch payment statistics' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedGET = withAuth(GET, ['ADMIN']);

export { protectedGET as GET };
