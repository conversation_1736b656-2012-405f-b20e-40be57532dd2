"use client";

import { motion } from 'framer-motion';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  change?: {
    value: number;
    isPositive: boolean;
  };
  index?: number;
}

export default function StatCard({ title, value, icon, color, change, index = 0 }: StatCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, duration: 0.5 }}
      className="bg-white rounded-lg shadow-sm p-6 border border-gray-200"
    >
      <div className="flex items-center">
        <div className={`p-3 rounded-full ${color} bg-opacity-10 mr-4`}>
          <div className={`text-${color.split('-')[1]}-500`}>
            {icon}
          </div>
        </div>
        <div>
          <p className="text-sm text-gray-500">{title}</p>
          <div className="flex items-center">
            <p className="text-2xl font-bold">{value}</p>
            {change && (
              <span className={`ml-2 text-sm ${change.isPositive ? 'text-green-500' : 'text-red-500'}`}>
                {change.isPositive ? '↑' : '↓'} {Math.abs(change.value)}%
              </span>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}
