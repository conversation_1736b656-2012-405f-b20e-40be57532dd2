import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET utility readings for a specific room
async function handleGet(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const { roomId } = params;
    
    // Get query parameters
    const url = new URL(request.url);
    const isPaid = url.searchParams.get('isPaid');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const limit = parseInt(url.searchParams.get('limit') || '12'); // Default to last 12 readings
    
    // Check if room exists
    const room = await prisma.room.findUnique({
      where: { id: roomId }
    });

    if (!room) {
      return NextResponse.json(
        { success: false, message: 'Room not found' },
        { status: 404 }
      );
    }

    // Build the where clause
    const where: any = { roomId };
    
    if (isPaid !== null) {
      where.isPaid = isPaid === 'true';
    }
    
    // Add date range filter if provided
    if (startDate || endDate) {
      where.readingDate = {};
      
      if (startDate) {
        where.readingDate.gte = new Date(startDate);
      }
      
      if (endDate) {
        where.readingDate.lte = new Date(endDate);
      }
    }
    
    // Get utility readings for the room
    const utilityReadings = await prisma.utilityReading.findMany({
      where,
      orderBy: {
        readingDate: 'desc'
      },
      take: limit
    });

    // Get the latest reading
    const latestReading = utilityReadings.length > 0 ? utilityReadings[0] : null;
    
    // Get the previous reading before the latest one
    const previousReading = utilityReadings.length > 1 ? utilityReadings[1] : null;
    
    // Calculate monthly statistics
    const monthlyStats = utilityReadings.reduce((acc: any, reading) => {
      const date = new Date(reading.readingDate);
      const monthYear = `${date.getFullYear()}-${date.getMonth() + 1}`;
      
      if (!acc[monthYear]) {
        acc[monthYear] = {
          month: date.getMonth() + 1,
          year: date.getFullYear(),
          monthName: date.toLocaleString('default', { month: 'long' }),
          waterUsage: 0,
          electricityUsage: 0,
          waterCost: 0,
          electricityCost: 0,
          totalCost: 0,
          isPaid: reading.isPaid
        };
      }
      
      acc[monthYear].waterUsage += reading.waterUsage || 0;
      acc[monthYear].electricityUsage += reading.electricityUsage || 0;
      acc[monthYear].waterCost += reading.waterCost || 0;
      acc[monthYear].electricityCost += reading.electricityCost || 0;
      acc[monthYear].totalCost += reading.totalCost || 0;
      
      // A month is considered paid only if all readings are paid
      acc[monthYear].isPaid = acc[monthYear].isPaid && reading.isPaid;
      
      return acc;
    }, {});
    
    // Convert to array and sort by date (newest first)
    const monthlyStatsArray = Object.values(monthlyStats).sort((a: any, b: any) => {
      if (a.year !== b.year) return b.year - a.year;
      return b.month - a.month;
    });

    return NextResponse.json({
      success: true,
      data: {
        room: {
          id: room.id,
          roomNumber: room.roomNumber
        },
        readings: utilityReadings,
        latestReading,
        previousReading,
        monthlyStats: monthlyStatsArray
      }
    });
  } catch (error) {
    console.error(`Error fetching utility readings for room with ID ${params.roomId}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch utility readings for room' },
      { status: 500 }
    );
  }
}

// Export the handlers with authentication middleware
export const GET = withAuth(handleGet, ['ADMIN']);
