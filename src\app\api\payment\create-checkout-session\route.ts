import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';
import Stripe from 'stripe';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_your_test_key');

async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { contractId, paymentId, items } = body;

    // Get the authenticated user from the request
    const user = (request as any).user;

    // Validate required fields
    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Invalid request data: items are required' },
        { status: 400 }
      );
    }

    // Check if this is a contract payment or individual payment
    if (contractId) {
      // Contract payment flow
      // Get the contract
      const contract = await prisma.contract.findUnique({
        where: { id: contractId },
        include: {
          room: true
        }
      });

      if (!contract) {
        return NextResponse.json(
          { success: false, message: 'Contract not found' },
          { status: 404 }
        );
      }

      // Verify that the contract belongs to the user
      if (contract.residentId !== user.id && user.role !== 'ADMIN') {
        return NextResponse.json(
          { success: false, message: 'Unauthorized to access this contract' },
          { status: 403 }
        );
      }

      // Create line items for Stripe
      const lineItems = items.map(item => ({
        price_data: {
          currency: 'thb',
          product_data: {
            name: item.name,
          },
          unit_amount: Math.round(item.amount * 100), // Convert to cents
        },
        quantity: item.quantity || 1,
      }));

      // Create a Stripe checkout session
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['promptpay'],
        line_items: lineItems,
        mode: 'payment',
        success_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/payment/${contractId}`,
        metadata: {
          contractId,
          userId: user.id,
          roomId: contract.roomId,
          isContractPayment: 'true'
        },
        customer_email: user.email,
      });

      return NextResponse.json({
        success: true,
        url: session.url
      });
    } else if (paymentId) {
      // Individual payment flow
      // Get the payment
      const payment = await prisma.payment.findUnique({
        where: { id: paymentId },
        include: {
          room: true
        }
      });

      if (!payment) {
        return NextResponse.json(
          { success: false, message: 'Payment not found' },
          { status: 404 }
        );
      }

      // Verify that the payment belongs to the user
      if (payment.userId !== user.id && user.role !== 'ADMIN') {
        return NextResponse.json(
          { success: false, message: 'Unauthorized to access this payment' },
          { status: 403 }
        );
      }

      // Create line items for Stripe
      const lineItems = items.map(item => ({
        price_data: {
          currency: 'thb',
          product_data: {
            name: item.name,
          },
          unit_amount: Math.round(item.amount * 100), // Convert to cents
        },
        quantity: item.quantity || 1,
      }));

      // Create a Stripe checkout session
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['promptpay'],
        line_items: lineItems,
        mode: 'payment',
        success_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/payment/invoice/${paymentId}`,
        metadata: {
          paymentId,
          userId: user.id,
          roomId: payment.roomId,
          isContractPayment: 'false'
        },
        customer_email: user.email,
      });

      return NextResponse.json({
        success: true,
        url: session.url
      });
    } else {
      return NextResponse.json(
        { success: false, message: 'Either contractId or paymentId is required' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedPOST = withAuth(POST);

export { protectedPOST as POST };
