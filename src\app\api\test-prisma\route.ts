import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    // Test the Prisma client connection
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    return NextResponse.json({ status: 'success', message: 'Prisma client is working correctly', result });
  } catch (error) {
    console.error('Error testing Prisma client:', error);
    return NextResponse.json(
      { status: 'error', message: 'Prisma client connection failed', error: String(error) },
      { status: 500 }
    );
  }
}
