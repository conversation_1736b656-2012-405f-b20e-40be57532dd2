import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET a specific room type by ID
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;

    const roomType = await prisma.roomType.findUnique({
      where: { id },
      include: {
        _count: {
          select: { rooms: true }
        }
      }
    });

    if (!roomType) {
      return NextResponse.json(
        { success: false, message: 'Room type not found' },
        { status: 404 }
      );
    }

    // Transform the amenities string to array
    const transformedRoomType = {
      ...roomType,
      amenities: roomType.amenities ? JSON.parse(roomType.amenities) : [],
      roomCount: roomType._count.rooms
    };

    return NextResponse.json({
      success: true,
      data: transformedRoomType
    });
  } catch (error) {
    const params = await context.params;
    console.error(`Error fetching room type with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch room type' },
      { status: 500 }
    );
  }
}

// PUT update a room type
async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const body = await request.json();
    const { name, description, price, amenities, imageUrl, imageData, fileType, fileName } = body;

    // Validate required fields
    if (!name || !price) {
      return NextResponse.json(
        { success: false, message: 'Name and price are required' },
        { status: 400 }
      );
    }

    // Check if room type exists
    const existingRoomType = await prisma.roomType.findUnique({
      where: { id }
    });

    if (!existingRoomType) {
      return NextResponse.json(
        { success: false, message: 'Room type not found' },
        { status: 404 }
      );
    }

    // Check if another room type with the same name exists (excluding this one)
    if (name !== existingRoomType.name) {
      const duplicateName = await prisma.roomType.findUnique({
        where: { name }
      });

      if (duplicateName) {
        return NextResponse.json(
          { success: false, message: 'Another room type with this name already exists' },
          { status: 409 }
        );
      }
    }

    // Update the room type with image data directly in the RoomType model
    const updatedRoomType = await prisma.roomType.update({
      where: { id },
      data: {
        name,
        description,
        price: parseFloat(price.toString()),
        amenities: Array.isArray(amenities) ? JSON.stringify(amenities) : null,
        imageUrl,
        // Only update image fields if new data is provided
        ...(imageData ? { imageData, fileType, fileName } : {})
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Room type updated successfully',
      data: {
        ...updatedRoomType,
        amenities: amenities || [],
        imageData: imageData || null
      }
    });
  } catch (error) {
    // Provide more detailed error message
    const errorMessage = error instanceof Error
      ? `Failed to update room type: ${error.message}`
      : 'Failed to update room type';

    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: 500 }
    );
  }
}

// DELETE a room type
async function DELETE(
  _request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;

    // Check if room type exists
    const existingRoomType = await prisma.roomType.findUnique({
      where: { id },
      include: {
        rooms: true
      }
    });

    if (!existingRoomType) {
      return NextResponse.json(
        { success: false, message: 'Room type not found' },
        { status: 404 }
      );
    }

    // Check if there are rooms associated with this room type
    if (existingRoomType.rooms.length > 0) {
      return NextResponse.json(
        {
          success: false,
          message: 'Cannot delete room type with associated rooms. Please delete or reassign the rooms first.'
        },
        { status: 400 }
      );
    }

    // Delete the room type
    await prisma.roomType.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Room type deleted successfully'
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, message: 'Failed to delete room type' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware to PUT and DELETE methods
const protectedPUT = withAuth(PUT, ['ADMIN']);
const protectedDELETE = withAuth(DELETE, ['ADMIN']);

export { protectedPUT as PUT, protectedDELETE as DELETE };
