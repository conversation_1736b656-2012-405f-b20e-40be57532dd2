"use client";

import { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface RoomCardProps {
  id: string;
  title: string;
  price: string;
  description: string;
  imgSrc: string;
  features: string[];
  size: string;
  capacity: number;
  availability: string;
  type: string;
}

export default function RoomCard({
  id,
  title,
  price,
  description,
  imgSrc,
  features,
  size,
  capacity,
  availability,
  type
}: RoomCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 h-full flex flex-col"
      whileHover={{
        y: -5,
        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        transition: { duration: 0.2 }
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div className="relative h-64 overflow-hidden">
        {/* Use regular img tag for all images to avoid Next.js Image optimization issues */}
        <img
          src={imgSrc.startsWith('data:') || imgSrc.startsWith('/') ? imgSrc : '/images/noobroom.jpg'}
          alt={title}
          className="object-cover w-full h-full transition-transform duration-700 ease-in-out"
          style={{ transform: isHovered ? 'scale(1.05)' : 'scale(1)' }}
          onError={(e) => {
            // Fallback to default image if there's an error loading the image
            e.currentTarget.src = '/images/noobroom.jpg';
          }}
        />
        <div className="absolute top-0 right-0 bg-amber-500 text-white px-3 py-1 m-4 rounded-full text-sm font-medium">
          {availability}
        </div>
        <div className="absolute bottom-0 left-0 bg-gradient-to-t from-black/70 to-transparent w-full h-20 flex items-end">
          <div className="p-4">
            <span className="text-white text-xs font-medium px-2 py-1 bg-amber-500/80 rounded-full">
              {type}
            </span>
          </div>
        </div>
      </div>

      <div className="p-6 flex-grow flex flex-col">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-xl font-bold text-gray-900">{title}</h3>
          <span className="text-amber-600 font-bold">{price}</span>
        </div>

        <p className="text-gray-600 mb-4 text-sm">{description}</p>

        <div className="grid grid-cols-2 gap-2 mb-4">
          <div className="flex items-center text-sm text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
            </svg>
            {size}
          </div>
          <div className="flex items-center text-sm text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            {capacity} {capacity === 1 ? 'Person' : 'People'}
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mb-4">
          {features.slice(0, 3).map((feature, index) => (
            <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
              {feature}
            </span>
          ))}
          {features.length > 3 && (
            <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
              +{features.length - 3} more
            </span>
          )}
        </div>

        <div className="mt-auto">
          <Link href={`/rooms/${id}`} className="block w-full">
            <motion.button
              className="w-full bg-amber-500 hover:bg-amber-600 text-white py-2 rounded-lg transition-colors font-medium text-sm"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              View Details
            </motion.button>
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
