import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user exists
    if (!user || !user.id) {
      return NextResponse.json(
        { success: false, message: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get user's room
    const userRoom = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        rooms: true
      }
    });

    if (!userRoom || !userRoom.rooms || userRoom.rooms.length === 0) {
      return NextResponse.json(
        { success: false, message: 'User does not have an assigned room' },
        { status: 404 }
      );
    }

    const roomId = userRoom.rooms[0].id;

    // Get utility readings for the room
    const utilityReadings = await prisma.utilityReading.findMany({
      where: {
        roomId: roomId
      },
      orderBy: {
        readingDate: 'desc'
      },
      take: 7 // Get the last 7 readings for history
    });

    if (utilityReadings.length < 2) {
      return NextResponse.json(
        { success: false, message: 'Not enough utility readings available' },
        { status: 404 }
      );
    }

    // Current is the most recent, previous is the second most recent
    const current = utilityReadings[0];
    const previous = utilityReadings[1];

    // Assume rates (these could be stored in a settings table in a real app)
    const waterRate = 18; // baht per cubic meter
    const electricityRate = 4; // baht per kWh

    // Calculate water and electricity usage
    const waterUsage = current.waterReading - previous.waterReading;
    const electricityUsage = current.electricityReading - previous.electricityReading;

    // Create history arrays for the charts
    const waterHistory = utilityReadings.slice().reverse().map((reading, index, array) => {
      // For the first reading, we don't have a previous reading to compare with
      if (index === 0) {
        return {
          month: new Date(reading.readingDate).toLocaleString('default', { month: 'short' }),
          usage: 0
        };
      }
      
      // Calculate usage from previous reading
      const previousReading = array[index - 1];
      const usage = reading.waterReading - previousReading.waterReading;
      
      return {
        month: new Date(reading.readingDate).toLocaleString('default', { month: 'short' }),
        usage: usage
      };
    }).slice(1); // Remove the first entry which has 0 usage

    const electricityHistory = utilityReadings.slice().reverse().map((reading, index, array) => {
      // For the first reading, we don't have a previous reading to compare with
      if (index === 0) {
        return {
          month: new Date(reading.readingDate).toLocaleString('default', { month: 'short' }),
          usage: 0
        };
      }
      
      // Calculate usage from previous reading
      const previousReading = array[index - 1];
      const usage = reading.electricityReading - previousReading.electricityReading;
      
      return {
        month: new Date(reading.readingDate).toLocaleString('default', { month: 'short' }),
        usage: usage
      };
    }).slice(1); // Remove the first entry which has 0 usage

    // Construct the response
    const utilitiesData = {
      water: {
        current: current.waterReading,
        previous: previous.waterReading,
        usage: waterUsage,
        rate: waterRate,
        cost: waterUsage * waterRate,
        history: waterHistory
      },
      electricity: {
        current: current.electricityReading,
        previous: previous.electricityReading,
        usage: electricityUsage,
        rate: electricityRate,
        cost: electricityUsage * electricityRate,
        history: electricityHistory
      },
      totalCost: (waterUsage * waterRate) + (electricityUsage * electricityRate),
      readingDate: current.readingDate
    };

    return NextResponse.json({
      success: true,
      data: utilitiesData
    });
  } catch (error) {
    console.error('Error fetching utility data:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch utility data' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedGET = withAuth(GET, ['ADMIN', 'RESIDENT', 'VISITORS']);

export { protectedGET as GET };
