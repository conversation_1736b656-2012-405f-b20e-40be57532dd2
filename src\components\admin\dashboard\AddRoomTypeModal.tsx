"use client";

import { useState } from 'react';
import Modal from '@/components/admin/Modal';
import Form<PERSON>ield from '@/components/admin/FormField';

interface AddRoomTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddRoomType: (roomType: any) => void;
  amenitiesList: string[];
}

export default function AddRoomTypeModal({
  isOpen,
  onClose,
  onAddRoomType,
  amenitiesList
}: AddRoomTypeModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: 0,
    amenities: ['Wi-Fi'],
    imageUrl: '',
    imageData: '',
    fileType: '',
    fileName: ''
  });
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'price' ? Number(value) : value
    });

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size - limit to 2MB
    if (file.size > 2 * 1024 * 1024) {
      setFormErrors({
        ...formErrors,
        imageUrl: 'Image size should be less than 2MB'
      });
      return;
    }

    // For preview purposes
    const previewUrl = URL.createObjectURL(file);
    setImagePreview(previewUrl);

    // Store file name and type immediately
    setFormData({
      ...formData,
      imageUrl: file.name,
      fileType: file.type,
      fileName: file.name
    });

    // Read the file as a data URL to store in the database
    const reader = new FileReader();
    reader.onloadend = () => {
      const imageDataUrl = reader.result as string;

      // Store the image data
      setFormData(prevData => ({
        ...prevData,
        imageData: imageDataUrl // The base64 data to store in the database
      }));
    };
    reader.readAsDataURL(file);

    // Clear error for this field
    if (formErrors.imageUrl) {
      setFormErrors({
        ...formErrors,
        imageUrl: ''
      });
    }
  };

  const handleAmenityChange = (amenity: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        amenities: [...formData.amenities, amenity]
      });
    } else {
      setFormData({
        ...formData,
        amenities: formData.amenities.filter(a => a !== amenity)
      });
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Room type name is required';
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required';
    }

    if (formData.price <= 0) {
      errors.price = 'Price must be greater than 0';
    }

    if (!formData.imageUrl) {
      errors.imageUrl = 'Room image is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddRoomType = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    onAddRoomType(formData);

    // Reset form
    setFormData({
      name: '',
      description: '',
      price: 0,
      amenities: ['Wi-Fi'],
      imageUrl: '',
      imageData: '',
      fileType: '',
      fileName: ''
    });
    setImagePreview(null);
    setFormErrors({});
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Add Room Type"
      footer={
        <>
          <button
            type="button"
            onClick={handleAddRoomType}
            className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-amber-500 text-base font-medium text-white hover:bg-amber-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Add Room Type
          </button>
          <button
            type="button"
            onClick={onClose}
            className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Cancel
          </button>
        </>
      }
    >
      <form onSubmit={handleAddRoomType}>
        <div className="space-y-4">
          <FormField
            label="Room Type Name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="Enter room type name"
            required
            error={formErrors.name}
          />
          <FormField
            label="Description"
            name="description"
            type="textarea"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Enter room description"
            required
            error={formErrors.description}
          />
          <FormField
            label="Price (฿/month)"
            name="price"
            type="number"
            value={formData.price}
            onChange={handleInputChange}
            required
            error={formErrors.price}
          />

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Room Image
              {formErrors.imageUrl && (
                <span className="text-red-500 text-xs ml-2">{formErrors.imageUrl}</span>
              )}
            </label>
            <div className="mt-1 flex items-center space-x-4">
              <div className="flex-shrink-0 h-24 w-24 rounded-md overflow-hidden bg-gray-100 border border-gray-200">
                {imagePreview ? (
                  <img
                    src={imagePreview}
                    alt="Room preview"
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="h-full w-full flex items-center justify-center text-gray-400">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                )}
              </div>
              <div className="flex-grow">
                <input
                  type="file"
                  id="image-upload"
                  accept="image/*"
                  className="sr-only"
                  onChange={handleImageChange}
                />
                <label
                  htmlFor="image-upload"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 cursor-pointer"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 -ml-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                  </svg>
                  Upload Image
                </label>
                <p className="mt-1 text-xs text-gray-500">
                  PNG, JPG, GIF up to 10MB
                </p>
              </div>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Amenities</label>
            <div className="grid grid-cols-2 gap-2">
              {amenitiesList.map((amenity) => (
                <div key={amenity} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`amenity-${amenity}`}
                    checked={formData.amenities.includes(amenity)}
                    onChange={(e) => handleAmenityChange(amenity, e.target.checked)}
                    className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
                  />
                  <label htmlFor={`amenity-${amenity}`} className="ml-2 block text-sm text-gray-700">
                    {amenity}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
      </form>
    </Modal>
  );
}
