"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';

interface PaymentData {
  id: string;
  amount: number;
  dueDate: string;
  status: string;
  type: string;
  userId: string;
  roomId: string;
  room: {
    roomNumber: string;
    roomType: {
      name: string;
    };
  };
}

export default function PaymentInvoicePage({ params }: { params: { paymentId: string } }) {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    // Redirect if user is not logged in
    if (!authLoading && !user) {
      router.push('/auth?redirect=' + encodeURIComponent(`/payment/invoice/${params.paymentId}`));
      return;
    }

    const fetchPaymentData = async () => {
      if (!user) return;

      try {
        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication token not found. Please log in again.');
          setIsLoading(false);
          return;
        }

        const response = await fetch(`/api/payments/${params.paymentId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch payment data: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
          setPaymentData(result.data);
        } else {
          setError(result.message || 'Failed to fetch payment data');
        }
      } catch (err) {
        setError('An error occurred while fetching payment data');
        console.error('Payment fetch error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchPaymentData();
    }
  }, [params.paymentId, user, authLoading, router]);

  const handlePayment = async () => {
    if (!user || !paymentData) return;

    setIsProcessing(true);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        setError('Authentication token not found. Please log in again.');
        setIsProcessing(false);
        return;
      }

      // Create checkout session
      const response = await fetch('/api/payment/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          paymentId: paymentData.id,
          items: [
            {
              name: `${paymentData.type === 'RENT' ? 'Rent' : 
                    paymentData.type === 'UTILITIES' ? 'Utilities' : 
                    paymentData.type === 'MAINTENANCE' ? 'Maintenance' : 
                    paymentData.type === 'OTHER' ? 'Other' : 
                    paymentData.type} - ${paymentData.room.roomType.name} (${paymentData.room.roomNumber})`,
              amount: paymentData.amount,
              quantity: 1
            }
          ]
        })
      });

      if (!response.ok) {
        // Log detailed error information
        const errorText = await response.text();
        console.error('Checkout session error:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        });

        setError(`Failed to create checkout session: ${response.status} ${response.statusText}`);
        setIsProcessing(false);
        return;
      }

      const result = await response.json();

      if (result.success) {
        // Redirect to Stripe checkout
        window.location.href = result.url;
      } else {
        setError(result.message || 'Failed to create payment session');
        setIsProcessing(false);
      }
    } catch (err) {
      setError('An error occurred while processing payment');
      console.error('Payment error:', err);
      setIsProcessing(false);
    }
  };

  // For demo purposes, let's add a function to simulate successful payment
  const handleDemoPayment = async () => {
    if (!user || !paymentData) return;

    setIsProcessing(true);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        setError('Authentication token not found. Please log in again.');
        setIsProcessing(false);
        return;
      }

      // Mark payment as paid
      const paymentResponse = await fetch(`/api/payments/${paymentData.id}/mark-paid`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          paymentMethod: 'Demo Payment',
          notes: 'Payment processed via demo payment option'
        })
      });

      if (!paymentResponse.ok) {
        // Log detailed error information
        const errorText = await paymentResponse.text();
        console.error('Payment processing error:', {
          status: paymentResponse.status,
          statusText: paymentResponse.statusText,
          body: errorText
        });

        setError(`Failed to process payment: ${paymentResponse.status} ${paymentResponse.statusText}`);
        setIsProcessing(false);
        return;
      }

      const paymentResult = await paymentResponse.json();

      if (paymentResult.success) {
        // Redirect to success page
        router.push(`/payment/success?session_id=demo_${Date.now()}`);
      } else {
        setError(paymentResult.message || 'Failed to process demo payment');
        setIsProcessing(false);
      }
    } catch (err) {
      setError('An error occurred while processing demo payment');
      console.error('Demo payment error:', err);
      setIsProcessing(false);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-8">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !paymentData) {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-8">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-700">{error || 'Payment not found'}</p>
          <button
            onClick={() => router.push('/user/dashboard')}
            className="mt-6 bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // Format date
  const dueDate = new Date(paymentData.dueDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-8">
        <h1 className="text-2xl font-bold mb-6">Payment Details</h1>

        <div className="mb-8 border-b pb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-lg font-semibold mb-4">Payment Information</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Type:</span>
                  <span className="font-medium">
                    {paymentData.type === 'RENT' ? 'Rent' : 
                     paymentData.type === 'UTILITIES' ? 'Utilities' : 
                     paymentData.type === 'MAINTENANCE' ? 'Maintenance' : 
                     paymentData.type === 'OTHER' ? 'Other' : 
                     paymentData.type}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-medium">฿{paymentData.amount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Due Date:</span>
                  <span className="font-medium">{dueDate}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className="px-3 py-1 rounded-full text-sm font-medium bg-amber-100 text-amber-800">
                    {paymentData.status}
                  </span>
                </div>
              </div>
            </div>
            <div>
              <h2 className="text-lg font-semibold mb-4">Room Information</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Room Number:</span>
                  <span className="font-medium">{paymentData.room.roomNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Room Type:</span>
                  <span className="font-medium">{paymentData.room.roomType.name}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Payment Method</h2>
          <p className="mb-4">
            We accept the following payment methods through our secure payment processor, Stripe:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="p-3 border rounded-lg flex items-center space-x-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
              <span>Credit/Debit Cards</span>
            </div>
            <div className="p-3 border rounded-lg flex items-center space-x-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <span>PromptPay</span>
            </div>
            <div className="p-3 border rounded-lg flex items-center space-x-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
              </svg>
              <span>Bank Transfer</span>
            </div>
          </div>
          <p className="text-sm text-gray-600">
            You'll be redirected to Stripe's secure payment page to complete your transaction.
          </p>
        </div>

        <div className="flex justify-between">
          <button
            onClick={() => router.push('/user/dashboard')}
            className="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-400 transition"
            disabled={isProcessing}
          >
            Back to Dashboard
          </button>

          {/* For production, use this button */}
          <button
            onClick={handlePayment}
            disabled={isProcessing}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition"
          >
            {isProcessing ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
            ) : (
              'Pay Now'
            )}
          </button>

          {/* For demo purposes, use this button */}
          <button
            onClick={handleDemoPayment}
            disabled={isProcessing}
            className="px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition"
          >
            {isProcessing ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
            ) : (
              'Demo Payment (Skip Stripe)'
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
