# Footer Components

This directory contains reusable footer components for the dormitory website.

## Available Components

### MainFooter

A comprehensive footer with full information, including:
- Social media links
- Quick navigation links
- Contact information
- Opening hours
- Copyright information
- Legal links

```jsx
import MainFooter from '@/components/layout/MainFooter';

// Usage
<MainFooter />
```

### CompactFooter

A simplified footer for pages where a more compact design is preferred:
- Minimal branding
- Essential navigation links
- Social media icons
- Copyright information

```jsx
import CompactFooter from '@/components/layout/CompactFooter';

// Usage
<CompactFooter />
```

### ThaiFooter

A localized Thai version of the footer with Thai text:
- Thai language content
- Same structure as MainFooter
- Includes Line social media icon

```jsx
import ThaiFooter from '@/components/layout/ThaiFooter';

// Usage
<ThaiFooter />
```

### FooterSelector

A utility component that allows you to choose between different footer styles:

```jsx
import FooterSelector from '@/components/layout/FooterSelector';

// Usage with type
<FooterSelector type="main" /> // Options: "main", "compact", "thai"

// Usage with language
<FooterSelector language="th" /> // Options: "en", "th"

// Usage with custom footer
<FooterSelector>
  <CustomFooter />
</FooterSelector>
```

## Features

- Responsive design that works on all screen sizes
- Framer Motion animations for enhanced user experience
- Consistent styling with the rest of the application
- Social media links with hover effects
- Proper semantic HTML structure
