import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// POST generate a bill/payment from utility readings
async function handlePost(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      readingIds, 
      dueDate, 
      notes 
    } = body;

    // Validate required fields
    if (!readingIds || !Array.isArray(readingIds) || readingIds.length === 0) {
      return NextResponse.json(
        { success: false, message: 'At least one reading ID is required' },
        { status: 400 }
      );
    }

    if (!dueDate) {
      return NextResponse.json(
        { success: false, message: 'Due date is required' },
        { status: 400 }
      );
    }

    // Get all the readings
    const readings = await prisma.utilityReading.findMany({
      where: {
        id: {
          in: readingIds
        }
      },
      include: {
        room: {
          include: {
            residents: true
          }
        }
      }
    });

    // Check if all readings exist
    if (readings.length !== readingIds.length) {
      return NextResponse.json(
        { success: false, message: 'One or more readings not found' },
        { status: 404 }
      );
    }

    // Check if any reading is already paid
    const paidReadings = readings.filter(reading => reading.isPaid);
    if (paidReadings.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: `${paidReadings.length} reading(s) are already paid. Cannot generate bill for paid readings.` 
        },
        { status: 400 }
      );
    }

    // Group readings by room
    const readingsByRoom = readings.reduce((acc: any, reading) => {
      if (!acc[reading.roomId]) {
        acc[reading.roomId] = {
          roomId: reading.roomId,
          roomNumber: reading.room.roomNumber,
          readings: [],
          totalAmount: 0,
          residents: reading.room.residents
        };
      }
      
      acc[reading.roomId].readings.push(reading);
      acc[reading.roomId].totalAmount += reading.totalCost || 0;
      
      return acc;
    }, {});

    // Create payments for each room
    const payments = [];
    const updatedReadings = [];

    for (const roomId in readingsByRoom) {
      const roomData = readingsByRoom[roomId];
      
      // Check if room has residents
      if (roomData.residents.length === 0) {
        return NextResponse.json(
          { 
            success: false, 
            message: `Room ${roomData.roomNumber} has no residents. Cannot generate bill.` 
          },
          { status: 400 }
        );
      }

      // Create payment
      const payment = await prisma.payment.create({
        data: {
          amount: roomData.totalAmount,
          dueDate: new Date(dueDate),
          status: 'PENDING',
          type: 'UTILITIES',
          notes: notes || `Utility bill for room ${roomData.roomNumber}`,
          userId: roomData.residents[0].id, // Assign to first resident
          roomId: roomId
        }
      });

      payments.push(payment);

      // Mark readings as paid
      for (const reading of roomData.readings) {
        const updatedReading = await prisma.utilityReading.update({
          where: { id: reading.id },
          data: { isPaid: true }
        });
        
        updatedReadings.push(updatedReading);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully generated ${payments.length} payment(s) for ${updatedReadings.length} utility reading(s)`,
      data: {
        payments,
        updatedReadings
      }
    });
  } catch (error) {
    console.error('Error generating bill from utility readings:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to generate bill from utility readings' },
      { status: 500 }
    );
  }
}

// Export the handlers with authentication middleware
export const POST = withAuth(handlePost, ['ADMIN']);
