import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET a specific service request by ID
async function handleGet(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Build the query
    const query: any = {
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true
          }
        },
        room: {
          select: {
            id: true,
            roomNumber: true,
            roomType: {
              select: {
                name: true
              }
            }
          }
        },
        responses: {
          orderBy: {
            createdAt: 'desc'
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                role: true
              }
            }
          }
        },
        images: true
      }
    };

    const serviceRequest = await prisma.serviceRequest.findUnique(query);

    if (!serviceRequest) {
      return NextResponse.json(
        { success: false, message: 'Service request not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: serviceRequest
    });
  } catch (error) {
    console.error(`Error fetching service request with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch service request' },
      { status: 500 }
    );
  }
}

// PUT update a service request (status, priority, assignedTo)
async function handlePut(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { status, priority, assignedTo } = body;

    // Check if service request exists
    const existingRequest = await prisma.serviceRequest.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true
          }
        },
        room: {
          select: {
            roomNumber: true
          }
        }
      }
    });

    if (!existingRequest) {
      return NextResponse.json(
        { success: false, message: 'Service request not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    if (status) {
      updateData.status = status;

      // If status is changed to RESOLVED, set resolvedAt
      if (status === 'RESOLVED' && existingRequest.status !== 'RESOLVED') {
        updateData.resolvedAt = new Date();
      }
    }

    if (priority) {
      updateData.priority = priority;
    }

    if (assignedTo !== undefined) {
      updateData.assignedTo = assignedTo;
    }

    // Update the service request
    const updatedRequest = await prisma.serviceRequest.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true
          }
        },
        room: {
          select: {
            roomNumber: true
          }
        }
      }
    });

    // Create a notification for the user
    if (status && status !== existingRequest.status) {
      await prisma.notification.create({
        data: {
          userId: existingRequest.userId,
          title: 'Service Request Update',
          message: `Your service request "${existingRequest.title}" status has been updated to ${status.replace('_', ' ').toLowerCase()}.`,
          type: 'SERVICE_REQUEST_UPDATE',
          link: `/resident/service-requests/${id}`
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Service request updated successfully',
      data: updatedRequest
    });
  } catch (error) {
    console.error(`Error updating service request with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to update service request' },
      { status: 500 }
    );
  }
}

// DELETE a service request
async function handleDelete(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Check if service request exists
    const existingRequest = await prisma.serviceRequest.findUnique({
      where: { id }
    });

    if (!existingRequest) {
      return NextResponse.json(
        { success: false, message: 'Service request not found' },
        { status: 404 }
      );
    }

    // Delete the service request
    await prisma.serviceRequest.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Service request deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting service request with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete service request' },
      { status: 500 }
    );
  }
}

// Export the handlers directly without authentication middleware
export const GET = handleGet;
export const PUT = handlePut;
export const DELETE = handleDelete;
