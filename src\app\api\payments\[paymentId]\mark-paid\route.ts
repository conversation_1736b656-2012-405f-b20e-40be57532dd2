import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

async function POST(
  request: NextRequest,
  { params }: { params: { paymentId: string } }
) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user exists
    if (!user || !user.id) {
      return NextResponse.json(
        { success: false, message: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get the payment ID from the URL
    const { paymentId } = params;

    // Get request body
    const body = await request.json();
    const { paymentMethod, notes } = body;

    // Find the payment
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        user: true,
        room: true
      }
    });

    if (!payment) {
      return NextResponse.json(
        { success: false, message: 'Payment not found' },
        { status: 404 }
      );
    }

    // Verify that the payment belongs to the user or the user is an admin
    if (payment.userId !== user.id && user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Update the payment status to PAID
    const updatedPayment = await prisma.payment.update({
      where: { id: paymentId },
      data: {
        status: 'PAID',
        paidDate: new Date(),
        paymentMethod: paymentMethod || 'Demo Payment',
        notes: notes || 'Payment processed via demo payment option',
        receiptNumber: `RCT-${new Date().getFullYear()}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Payment marked as paid successfully',
      data: updatedPayment
    });
  } catch (error) {
    console.error('Error marking payment as paid:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to mark payment as paid' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedPOST = withAuth(POST, ['ADMIN', 'RESIDENT']);

export { protectedPOST as POST };
