import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// POST assign a room to a user
async function handlePost(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { roomId } = body;

    // Validate required fields
    if (!roomId) {
      return NextResponse.json(
        { success: false, message: 'Room ID is required' },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        rooms: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Check if room exists
    const room = await prisma.room.findUnique({
      where: { id: roomId },
      include: {
        residents: true
      }
    });

    if (!room) {
      return NextResponse.json(
        { success: false, message: 'Room not found' },
        { status: 404 }
      );
    }

    // Check if room is already occupied
    if (room.isOccupied) {
      return NextResponse.json(
        { success: false, message: 'Room is already occupied' },
        { status: 400 }
      );
    }

    // Assign the room to the user and update room status
    const updatedUser = await prisma.$transaction(async (prisma) => {
      // Connect user to room
      const updatedUser = await prisma.user.update({
        where: { id },
        data: {
          rooms: {
            connect: { id: roomId }
          },
          // Update user role to RESIDENT when assigned a room
          role: 'RESIDENT'
        },
        include: {
          rooms: {
            include: {
              roomType: true
            }
          }
        }
      });

      // Mark room as occupied
      await prisma.room.update({
        where: { id: roomId },
        data: {
          isOccupied: true
        }
      });

      return updatedUser;
    });

    return NextResponse.json({
      success: true,
      message: 'Room assigned successfully',
      data: updatedUser
    });
  } catch (error) {
    console.error(`Error assigning room to user with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to assign room' },
      { status: 500 }
    );
  }
}

// DELETE remove a room assignment from a user
async function handleDelete(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const url = new URL(request.url);
    const roomId = url.searchParams.get('roomId');

    if (!roomId) {
      return NextResponse.json(
        { success: false, message: 'Room ID is required' },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        rooms: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Check if room exists
    const room = await prisma.room.findUnique({
      where: { id: roomId }
    });

    if (!room) {
      return NextResponse.json(
        { success: false, message: 'Room not found' },
        { status: 404 }
      );
    }

    // Check if user is assigned to this room
    const isAssigned = user.rooms.some(r => r.id === roomId);
    if (!isAssigned) {
      return NextResponse.json(
        { success: false, message: 'User is not assigned to this room' },
        { status: 400 }
      );
    }

    // Remove room assignment and update room status
    const updatedUser = await prisma.$transaction(async (prisma) => {
      // Disconnect user from room
      const updatedUser = await prisma.user.update({
        where: { id },
        data: {
          rooms: {
            disconnect: { id: roomId }
          }
        },
        include: {
          rooms: true
        }
      });

      // Mark room as unoccupied
      await prisma.room.update({
        where: { id: roomId },
        data: {
          isOccupied: false
        }
      });

      // If user has no rooms left, change role back to VISITORS
      if (updatedUser.rooms.length === 0) {
        await prisma.user.update({
          where: { id },
          data: {
            role: 'VISITORS'
          }
        });
      }

      return updatedUser;
    });

    return NextResponse.json({
      success: true,
      message: 'Room assignment removed successfully',
      data: updatedUser
    });
  } catch (error) {
    console.error(`Error removing room assignment from user with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to remove room assignment' },
      { status: 500 }
    );
  }
}

// Export the handlers with authentication middleware
export const POST = withAuth(handlePost, ['ADMIN']);
export const DELETE = withAuth(handleDelete, ['ADMIN']);
