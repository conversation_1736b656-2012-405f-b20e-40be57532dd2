// Payment service for handling payment-related operations

// Create a Stripe checkout session
export async function createCheckoutSession(data: {
  contractId: string;
  items: Array<{
    name: string;
    amount: number;
    quantity?: number;
  }>;
}) {
  const response = await fetch('/api/payment/create-checkout-session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify(data)
  });
  
  return response.json();
}

// Process a demo payment (for testing without <PERSON><PERSON>)
export async function processDemoPayment(data: {
  contractId: string;
  userId: string;
  roomId: string;
  rentAmount: number;
  depositAmount?: number;
}) {
  const response = await fetch('/api/payment/demo-success', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify(data)
  });
  
  return response.json();
}

// Get payment history for a user
export async function getUserPayments(userId: string) {
  const response = await fetch(`/api/payments/user/${userId}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }
  });
  
  return response.json();
}

// Get payment details by ID
export async function getPaymentById(id: string) {
  const response = await fetch(`/api/payments/${id}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }
  });
  
  return response.json();
}
