import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user exists
    if (!user || !user.id) {
      return NextResponse.json(
        { success: false, message: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get all payments for the user
    const payments = await prisma.payment.findMany({
      where: {
        userId: user.id
      },
      orderBy: {
        dueDate: 'desc'
      },
      include: {
        room: {
          select: {
            roomNumber: true,
            roomType: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    // Get payment summary
    const paidPayments = payments.filter(payment => payment.status === 'PAID');
    const pendingPayments = payments.filter(payment => payment.status === 'PENDING');
    const upcomingPayments = payments.filter(payment => payment.status === 'UPCOMING');
    const overduePayments = payments.filter(payment => payment.status === 'OVERDUE');

    // Calculate totals
    const paidTotal = paidPayments.reduce((sum, payment) => sum + payment.amount, 0);
    const pendingTotal = pendingPayments.reduce((sum, payment) => sum + payment.amount, 0);
    const upcomingTotal = upcomingPayments.reduce((sum, payment) => sum + payment.amount, 0);
    const overdueTotal = overduePayments.reduce((sum, payment) => sum + payment.amount, 0);

    // Get next payment due
    const nextPayment = await prisma.payment.findFirst({
      where: {
        userId: user.id,
        status: 'PENDING'
      },
      orderBy: {
        dueDate: 'asc'
      }
    });

    // Format the response
    const formattedPayments = payments.map(payment => ({
      id: payment.id,
      date: payment.dueDate,
      paidDate: payment.paidDate,
      amount: payment.amount,
      status: payment.status,
      type: payment.type,
      receiptNumber: payment.receiptNumber,
      paymentMethod: payment.paymentMethod,
      notes: payment.notes,
      room: payment.room ? {
        roomNumber: payment.room.roomNumber,
        roomType: payment.room.roomType.name
      } : null
    }));

    // Construct the response
    const paymentData = {
      payments: formattedPayments,
      summary: {
        paid: {
          count: paidPayments.length,
          total: paidTotal
        },
        pending: {
          count: pendingPayments.length,
          total: pendingTotal
        },
        upcoming: {
          count: upcomingPayments.length,
          total: upcomingTotal
        },
        overdue: {
          count: overduePayments.length,
          total: overdueTotal
        }
      },
      nextPayment: nextPayment ? {
        id: nextPayment.id,
        date: nextPayment.dueDate,
        amount: nextPayment.amount,
        type: nextPayment.type
      } : null
    };

    return NextResponse.json({
      success: true,
      data: paymentData
    });
  } catch (error) {
    console.error('Error fetching payment data:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch payment data' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedGET = withAuth(GET, ['ADMIN', 'RESIDENT', 'VISITORS']);

export { protectedGET as GET };
