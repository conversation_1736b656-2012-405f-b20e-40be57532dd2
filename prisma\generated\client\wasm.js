
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  name: 'name',
  role: 'role',
  phone: 'phone',
  address: 'address',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoomTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  price: 'price',
  amenities: 'amenities',
  imageUrl: 'imageUrl',
  imageData: 'imageData',
  fileType: 'fileType',
  fileName: 'fileName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoomScalarFieldEnum = {
  id: 'id',
  roomNumber: 'roomNumber',
  roomTypeId: 'roomTypeId',
  monthlyRate: 'monthlyRate',
  isOccupied: 'isOccupied',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoomImageScalarFieldEnum = {
  id: 'id',
  url: 'url',
  imageData: 'imageData',
  fileType: 'fileType',
  fileName: 'fileName',
  roomId: 'roomId',
  createdAt: 'createdAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  amount: 'amount',
  dueDate: 'dueDate',
  paidDate: 'paidDate',
  status: 'status',
  type: 'type',
  notes: 'notes',
  receiptNumber: 'receiptNumber',
  receiptUrl: 'receiptUrl',
  paymentMethod: 'paymentMethod',
  userId: 'userId',
  roomId: 'roomId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceRequestScalarFieldEnum = {
  id: 'id',
  type: 'type',
  title: 'title',
  description: 'description',
  status: 'status',
  priority: 'priority',
  userId: 'userId',
  roomId: 'roomId',
  assignedTo: 'assignedTo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  resolvedAt: 'resolvedAt'
};

exports.Prisma.ServiceRequestImageScalarFieldEnum = {
  id: 'id',
  url: 'url',
  serviceRequestId: 'serviceRequestId',
  createdAt: 'createdAt'
};

exports.Prisma.ServiceResponseScalarFieldEnum = {
  id: 'id',
  message: 'message',
  attachments: 'attachments',
  serviceRequestId: 'serviceRequestId',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UtilityReadingScalarFieldEnum = {
  id: 'id',
  roomId: 'roomId',
  waterReading: 'waterReading',
  electricityReading: 'electricityReading',
  readingDate: 'readingDate',
  waterUsage: 'waterUsage',
  electricityUsage: 'electricityUsage',
  waterCost: 'waterCost',
  electricityCost: 'electricityCost',
  totalCost: 'totalCost',
  isPaid: 'isPaid',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ActivityScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  performerId: 'performerId',
  action: 'action',
  details: 'details',
  target: 'target',
  targetId: 'targetId',
  targetType: 'targetType',
  ipAddress: 'ipAddress',
  timestamp: 'timestamp'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  message: 'message',
  type: 'type',
  isRead: 'isRead',
  link: 'link',
  createdAt: 'createdAt'
};

exports.Prisma.ContractScalarFieldEnum = {
  id: 'id',
  residentId: 'residentId',
  roomId: 'roomId',
  startDate: 'startDate',
  endDate: 'endDate',
  rentAmount: 'rentAmount',
  depositAmount: 'depositAmount',
  isActive: 'isActive',
  terminationDate: 'terminationDate',
  terminationReason: 'terminationReason',
  documentUrl: 'documentUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  name: 'name',
  phone: 'phone',
  address: 'address'
};

exports.Prisma.RoomTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  amenities: 'amenities',
  imageUrl: 'imageUrl',
  imageData: 'imageData',
  fileType: 'fileType',
  fileName: 'fileName'
};

exports.Prisma.RoomOrderByRelevanceFieldEnum = {
  id: 'id',
  roomNumber: 'roomNumber',
  roomTypeId: 'roomTypeId',
  description: 'description'
};

exports.Prisma.RoomImageOrderByRelevanceFieldEnum = {
  id: 'id',
  url: 'url',
  imageData: 'imageData',
  fileType: 'fileType',
  fileName: 'fileName',
  roomId: 'roomId'
};

exports.Prisma.PaymentOrderByRelevanceFieldEnum = {
  id: 'id',
  notes: 'notes',
  receiptNumber: 'receiptNumber',
  receiptUrl: 'receiptUrl',
  paymentMethod: 'paymentMethod',
  userId: 'userId',
  roomId: 'roomId'
};

exports.Prisma.ServiceRequestOrderByRelevanceFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  userId: 'userId',
  roomId: 'roomId',
  assignedTo: 'assignedTo'
};

exports.Prisma.ServiceRequestImageOrderByRelevanceFieldEnum = {
  id: 'id',
  url: 'url',
  serviceRequestId: 'serviceRequestId'
};

exports.Prisma.ServiceResponseOrderByRelevanceFieldEnum = {
  id: 'id',
  message: 'message',
  attachments: 'attachments',
  serviceRequestId: 'serviceRequestId',
  userId: 'userId'
};

exports.Prisma.UtilityReadingOrderByRelevanceFieldEnum = {
  id: 'id',
  roomId: 'roomId'
};

exports.Prisma.ActivityOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  performerId: 'performerId',
  action: 'action',
  details: 'details',
  target: 'target',
  targetId: 'targetId',
  ipAddress: 'ipAddress'
};

exports.Prisma.NotificationOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  message: 'message',
  link: 'link'
};

exports.Prisma.ContractOrderByRelevanceFieldEnum = {
  id: 'id',
  residentId: 'residentId',
  roomId: 'roomId',
  terminationReason: 'terminationReason',
  documentUrl: 'documentUrl'
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  RESIDENT: 'RESIDENT',
  VISITORS: 'VISITORS'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PAID: 'PAID',
  PENDING: 'PENDING',
  UPCOMING: 'UPCOMING',
  OVERDUE: 'OVERDUE',
  CANCELLED: 'CANCELLED'
};

exports.PaymentType = exports.$Enums.PaymentType = {
  RENT: 'RENT',
  UTILITIES: 'UTILITIES',
  DEPOSIT: 'DEPOSIT',
  MAINTENANCE: 'MAINTENANCE',
  OTHER: 'OTHER'
};

exports.ServiceRequestType = exports.$Enums.ServiceRequestType = {
  MAINTENANCE: 'MAINTENANCE',
  CLEANING: 'CLEANING',
  ELECTRICAL: 'ELECTRICAL',
  PLUMBING: 'PLUMBING',
  FURNITURE: 'FURNITURE',
  APPLIANCE: 'APPLIANCE',
  INTERNET: 'INTERNET',
  SECURITY: 'SECURITY',
  OTHER: 'OTHER'
};

exports.ServiceRequestStatus = exports.$Enums.ServiceRequestStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  RESOLVED: 'RESOLVED',
  CANCELLED: 'CANCELLED'
};

exports.Priority = exports.$Enums.Priority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT'
};

exports.TargetType = exports.$Enums.TargetType = {
  USER: 'USER',
  ROOM: 'ROOM',
  PAYMENT: 'PAYMENT',
  SERVICE_REQUEST: 'SERVICE_REQUEST',
  UTILITY: 'UTILITY',
  SYSTEM: 'SYSTEM'
};

exports.NotificationType = exports.$Enums.NotificationType = {
  PAYMENT_DUE: 'PAYMENT_DUE',
  PAYMENT_RECEIVED: 'PAYMENT_RECEIVED',
  SERVICE_REQUEST_UPDATE: 'SERVICE_REQUEST_UPDATE',
  ANNOUNCEMENT: 'ANNOUNCEMENT',
  SYSTEM: 'SYSTEM'
};

exports.Prisma.ModelName = {
  User: 'User',
  RoomType: 'RoomType',
  Room: 'Room',
  RoomImage: 'RoomImage',
  Payment: 'Payment',
  ServiceRequest: 'ServiceRequest',
  ServiceRequestImage: 'ServiceRequestImage',
  ServiceResponse: 'ServiceResponse',
  UtilityReading: 'UtilityReading',
  Activity: 'Activity',
  Notification: 'Notification',
  Contract: 'Contract'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
