### Get all utility readings
GET http://localhost:3000/api/utility-readings
Authorization: Bearer YOUR_TOKEN_HERE

### Get all utility readings with filtering
GET http://localhost:3000/api/utility-readings?roomId=ROOM_ID_HERE&isPaid=false&startDate=2023-01-01&endDate=2023-12-31&page=1&limit=10
Authorization: Bearer YOUR_TOKEN_HERE

### Get a specific utility reading
GET http://localhost:3000/api/utility-readings/READING_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

### Create a new utility reading
POST http://localhost:3000/api/utility-readings
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "roomId": "ROOM_ID_HERE",
  "waterReading": 150,
  "electricityReading": 500,
  "readingDate": "2023-11-01T00:00:00Z"
}

### Update a utility reading
PUT http://localhost:3000/api/utility-readings/READING_ID_HERE
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "waterReading": 155,
  "electricityReading": 510,
  "isPaid": true
}

### Delete a utility reading
DELETE http://localhost:3000/api/utility-readings/READING_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

### Get utility readings for a specific room
GET http://localhost:3000/api/utility-readings/room/ROOM_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

### Get utility readings for a specific room with filtering
GET http://localhost:3000/api/utility-readings/room/ROOM_ID_HERE?isPaid=false&startDate=2023-01-01&endDate=2023-12-31&limit=12
Authorization: Bearer YOUR_TOKEN_HERE

### Generate a bill from utility readings
POST http://localhost:3000/api/utility-readings/generate-bill
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "readingIds": ["READING_ID_1", "READING_ID_2"],
  "dueDate": "2023-12-15T00:00:00Z",
  "notes": "Utility bill for November 2023"
}
