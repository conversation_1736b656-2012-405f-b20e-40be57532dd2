"use client";

import { motion } from 'framer-motion';
import DataTable from '@/components/admin/DataTable';

interface Payment {
  id: number;
  user: string;
  room: string;
  amount: number;
  dueDate: string;
  status: string;
}

interface UpcomingPaymentsProps {
  payments: Payment[];
}

export default function UpcomingPayments({ payments }: UpcomingPaymentsProps) {
  const columns = [
    { key: 'user', header: 'User' },
    { key: 'room', header: 'Room' },
    { 
      key: 'amount', 
      header: 'Amount',
      render: (value: number) => `฿${value.toLocaleString()}`
    },
    { 
      key: 'dueDate', 
      header: 'Due Date',
      render: (value: string) => new Date(value).toLocaleDateString()
    },
    { 
      key: 'status', 
      header: 'Status',
      render: (value: string) => (
        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
          value === 'Paid' ? 'bg-green-100 text-green-800' : 'bg-amber-100 text-amber-800'
        }`}>
          {value}
        </span>
      )
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-semibold">Upcoming Payments</h2>
        <Link href="/cms/admin/payments" className="text-amber-600 text-sm hover:underline">
          View All
        </Link>
      </div>
      <DataTable data={payments} columns={columns} />
    </div>
  );
}
