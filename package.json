{"name": "dromistry-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "bunx prisma generate", "prisma:migrate": "bunx prisma migrate dev", "prisma:studio": "bunx prisma studio", "prisma:pull": "bunx prisma db pull", "prisma:push": "bunx prisma db push", "seed-payments": "node scripts/seed-payments.js", "prisma:seed": "bun prisma/seed.ts"}, "dependencies": {"@headlessui/react": "^2.2.2", "@prisma/client": "^6.9.0", "bcrypt": "^5.1.1", "framer-motion": "^12.8.0", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "mysql2": "^3.14.1", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-leaflet": "^5.0.0", "stripe": "^18.1.0", "sweetalert2": "^11.19.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/leaflet": "^1.9.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "prisma": "^6.9.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}, "prisma": {"seed": "bun prisma/seed.ts"}}