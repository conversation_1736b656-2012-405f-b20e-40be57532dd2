"use client";

import { useState } from 'react';

import Swal from 'sweetalert2';

interface ServiceRequestFormProps {
  roomNumber: string;
  onRequestSubmitted: () => void;
  onCancel?: () => void;
}

export default function ServiceRequestForm({ roomNumber, onRequestSubmitted, onCancel }: ServiceRequestFormProps) {
  const [requestType, setRequestType] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState('medium');
  const [availableDates, setAvailableDates] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const requestTypes = [
    { id: 'plumbing', label: 'Plumbing Issue' },
    { id: 'electrical', label: 'Electrical Problem' },
    { id: 'aircon', label: 'Air Conditioning' },
    { id: 'furniture', label: 'Furniture Repair' },
    { id: 'cleaning', label: 'Cleaning Service' },
    { id: 'internet', label: 'Internet Connection' },
    { id: 'other', label: 'Other' },
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!requestType) {
      newErrors.requestType = 'Please select a request type';
    }

    if (!description.trim()) {
      newErrors.description = 'Please provide a description of the issue';
    } else if (description.trim().length < 10) {
      newErrors.description = 'Description should be at least 10 characters';
    }

    if (!availableDates.trim()) {
      newErrors.availableDates = 'Please provide available dates for service';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Check if room number is valid
    if (roomNumber === "Not assigned") {
      Swal.fire({
        title: 'Room Error',
        text: 'You must have a room assigned to submit a service request. Please contact the administrator.',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
      });
      return;
    }

    setIsSubmitting(true);

    // Get token from localStorage
    const token = localStorage.getItem('token');
    if (!token) {
      Swal.fire({
        title: 'Authentication Error',
        text: 'You must be logged in to submit a service request.',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
      });
      setIsSubmitting(false);
      return;
    }

    // Map request type to API expected format
    const typeMapping: Record<string, string> = {
      'plumbing': 'PLUMBING',
      'electrical': 'ELECTRICAL',
      'aircon': 'APPLIANCE',
      'furniture': 'FURNITURE',
      'cleaning': 'CLEANING',
      'internet': 'INTERNET',
      'other': 'OTHER'
    };

    // Map priority to API expected format
    const priorityMapping: Record<string, string> = {
      'low': 'LOW',
      'medium': 'MEDIUM',
      'high': 'HIGH',
      'emergency': 'URGENT'
    };

    try {
      // Get room ID from roomNumber
      const roomResponse = await fetch(`/api/rooms?roomNumber=${encodeURIComponent(roomNumber)}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const roomData = await roomResponse.json();

      if (!roomResponse.ok || !roomData.success || !roomData.data.length) {
        throw new Error('Could not find your room. Please contact support.');
      }

      const roomId = roomData.data[0].id;

      // Create the service request
      console.log('Submitting service request with token:', token ? 'Token exists' : 'No token');

      const requestBody = {
        title: `${requestType.charAt(0).toUpperCase() + requestType.slice(1)} Issue`,
        description: `${description}\n\nAvailable dates: ${availableDates}`,
        type: typeMapping[requestType] || 'OTHER',
        priority: priorityMapping[priority] || 'MEDIUM',
        roomId: roomId,
        images: [] // No images for now
      };

      console.log('Request body:', requestBody);

      const response = await fetch('/api/service-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();
      console.log('Response status:', response.status);
      console.log('Response data:', data);

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        } else if (response.status === 403) {
          throw new Error('You do not have permission to submit service requests. Only residents can submit requests.');
        } else {
          throw new Error(data.message || 'Failed to submit service request');
        }
      }

      // Show success message using SweetAlert2
      Swal.fire({
        title: 'Request Submitted!',
        text: 'Your service request has been submitted successfully. We will contact you shortly.',
        icon: 'success',
        confirmButtonColor: '#f59e0b',
      });

      // Reset form
      setRequestType('');
      setDescription('');
      setPriority('medium');
      setAvailableDates('');

      // Notify parent component
      onRequestSubmitted();
    } catch (error) {
      console.error('Error submitting service request:', error);
      Swal.fire({
        title: 'Error',
        text: error instanceof Error ? error.message : 'There was an error submitting your request. Please try again.',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
      <h2 className="text-xl font-semibold mb-6">Submit a Service Request</h2>

      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Request Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Request Type <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {requestTypes.map((type) => (
                <div key={type.id}>
                  <input
                    type="radio"
                    id={`type-${type.id}`}
                    name="requestType"
                    value={type.id}
                    className="sr-only"
                    checked={requestType === type.id}
                    onChange={() => setRequestType(type.id)}
                  />
                  <label
                    htmlFor={`type-${type.id}`}
                    className={`block w-full text-center px-3 py-2 text-sm rounded-lg cursor-pointer transition-colors ${
                      requestType === type.id
                        ? 'bg-amber-50 text-amber-700 border-2 border-amber-500'
                        : 'bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    {type.label}
                  </label>
                </div>
              ))}
            </div>
            {errors.requestType && (
              <p className="mt-1 text-sm text-red-600">{errors.requestType}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              rows={4}
              className={`w-full px-3 py-2 text-gray-700 border ${
                errors.description ? 'border-red-500' : 'border-gray-300'
              } rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500`}
              placeholder="Please describe the issue in detail..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            ></textarea>
            {errors.description ? (
              <p className="mt-1 text-sm text-red-600">{errors.description}</p>
            ) : (
              <p className="mt-1 text-xs text-gray-500">
                Please provide as much detail as possible to help us address your request efficiently.
              </p>
            )}
          </div>

          {/* Priority */}
          <div>
            <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
              Priority
            </label>
            <select
              id="priority"
              className="w-full px-3 py-2 text-gray-700 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              value={priority}
              onChange={(e) => setPriority(e.target.value)}
            >
              <option value="low">Low - Not urgent</option>
              <option value="medium">Medium - Needs attention within a few days</option>
              <option value="high">High - Requires prompt attention</option>
              <option value="emergency">Emergency - Immediate attention needed</option>
            </select>
          </div>

          {/* Available Dates */}
          <div>
            <label htmlFor="availableDates" className="block text-sm font-medium text-gray-700 mb-1">
              Available Dates for Service <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="availableDates"
              className={`w-full px-3 py-2 text-gray-700 border ${
                errors.availableDates ? 'border-red-500' : 'border-gray-300'
              } rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500`}
              placeholder="e.g., Weekdays after 5 PM, or specific dates"
              value={availableDates}
              onChange={(e) => setAvailableDates(e.target.value)}
            />
            {errors.availableDates && (
              <p className="mt-1 text-sm text-red-600">{errors.availableDates}</p>
            )}
          </div>

          {/* Room Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Request Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Room Number:</span>
                <span className="ml-2 font-medium">{roomNumber}</span>
              </div>
              <div>
                <span className="text-gray-500">Submission Date:</span>
                <span className="ml-2 font-medium">{new Date().toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3">
            {onCancel && (
              <button
                type="button"
                onClick={onCancel}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition"
              >
                Cancel
              </button>
            )}
            <button
              type="submit"
              disabled={isSubmitting}
              className={`px-6 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition ${
                isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {isSubmitting ? (
                <>
                  <span className="inline-block animate-spin mr-2">⟳</span>
                  Submitting...
                </>
              ) : (
                'Submit Request'
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
