import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET all responses for a specific service request
async function handleGet(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before destructuring
    const { id } = await params;

    // Check if service request exists
    const serviceRequest = await prisma.serviceRequest.findUnique({
      where: { id },
      select: {
        id: true
      }
    });

    if (!serviceRequest) {
      return NextResponse.json(
        { success: false, message: 'Service request not found' },
        { status: 404 }
      );
    }

    // Get all responses for the service request
    const responses = await prisma.serviceResponse.findMany({
      where: {
        serviceRequestId: id
      },
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: responses
    });
  } catch (error) {
    console.error(`Error fetching responses for service request with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch responses' },
      { status: 500 }
    );
  }
}

// Export the handler with authentication middleware but allow all roles
export const GET = withAuth(handleGet);
