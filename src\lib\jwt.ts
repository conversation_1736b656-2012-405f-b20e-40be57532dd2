import jwt, { JwtPayload } from 'jsonwebtoken';

// Get JWT secret from environment variables or use a default for development
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-for-development';

// Token expiration time (e.g., 7 days)
const EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// Define the user payload interface
export interface UserPayload extends JwtPayload {
  id: string;
  email: string;
  name: string;
  role: string;
}

export function signJwtToken(payload: any) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: EXPIRES_IN });
}

export function verifyJwtToken(token: string): UserPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as UserPayload;
  } catch (error) {
    return null;
  }
}
