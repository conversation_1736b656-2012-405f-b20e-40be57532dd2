"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import Swal from 'sweetalert2';

interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
}

interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  app: boolean;
}

interface UserData {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  createdAt: string;
  updatedAt: string;
}

interface SettingsData {
  user: UserData;
  notifications: NotificationPreferences;
  emergencyContact: EmergencyContact;
  passwordLastChanged: string;
}

export default function SettingsPage() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState<SettingsData | null>(null);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isEditingEmergency, setIsEditingEmergency] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [formData, setFormData] = useState<any>(null);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  useEffect(() => {
    const fetchSettingsData = async () => {
      if (!user) return;

      try {
        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication token not found. Please log in again.');
          setIsLoading(false);
          return;
        }

        const response = await fetch('/api/dashboard/settings', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch settings data: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
          setUserData(result.data);
          setFormData(result.data);
        } else {
          setError(result.message || 'Failed to fetch settings data');
        }
      } catch (err) {
        console.error('Error fetching settings data:', err);
        setError('An error occurred while fetching settings data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettingsData();
  }, [user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;

    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveProfile = async () => {
    if (!userData || !formData) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        Swal.fire({
          title: 'Error',
          text: 'Authentication token not found. Please log in again.',
          icon: 'error',
          confirmButtonColor: '#f59e0b',
        });
        return;
      }

      const response = await fetch('/api/dashboard/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          profile: {
            name: formData.user.name,
            email: formData.user.email,
            phone: formData.user.phone,
            address: formData.user.address
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to update profile: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setUserData(formData);
        setIsEditingProfile(false);
        Swal.fire({
          title: 'Success',
          text: 'Profile updated successfully',
          icon: 'success',
          confirmButtonColor: '#f59e0b',
        });
      } else {
        throw new Error(result.message || 'Failed to update profile');
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      Swal.fire({
        title: 'Error',
        text: err instanceof Error ? err.message : 'An error occurred while updating profile',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
      });
    }
  };

  const handleSaveEmergency = async () => {
    if (!userData || !formData) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        Swal.fire({
          title: 'Error',
          text: 'Authentication token not found. Please log in again.',
          icon: 'error',
          confirmButtonColor: '#f59e0b',
        });
        return;
      }

      // In a real app, we would send the emergency contact data to the server
      // For now, we'll just update the local state
      setUserData(formData);
      setIsEditingEmergency(false);
      Swal.fire({
        title: 'Success',
        text: 'Emergency contact updated successfully',
        icon: 'success',
        confirmButtonColor: '#f59e0b',
      });
    } catch (err) {
      console.error('Error updating emergency contact:', err);
      Swal.fire({
        title: 'Error',
        text: err instanceof Error ? err.message : 'An error occurred while updating emergency contact',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
      });
    }
  };

  const handleSavePassword = async () => {
    if (!userData) return;

    // Validate passwords
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      Swal.fire({
        title: 'Error',
        text: 'New password and confirmation do not match',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
      });
      return;
    }

    if (passwordData.newPassword.length < 8) {
      Swal.fire({
        title: 'Error',
        text: 'New password must be at least 8 characters long',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
      });
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        Swal.fire({
          title: 'Error',
          text: 'Authentication token not found. Please log in again.',
          icon: 'error',
          confirmButtonColor: '#f59e0b',
        });
        return;
      }

      const response = await fetch('/api/dashboard/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          password: {
            currentPassword: passwordData.currentPassword,
            newPassword: passwordData.newPassword
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to update password: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setIsChangingPassword(false);
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
        Swal.fire({
          title: 'Success',
          text: 'Password updated successfully',
          icon: 'success',
          confirmButtonColor: '#f59e0b',
        });
      } else {
        throw new Error(result.message || 'Failed to update password');
      }
    } catch (err) {
      console.error('Error updating password:', err);
      Swal.fire({
        title: 'Error',
        text: err instanceof Error ? err.message : 'An error occurred while updating password',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
      });
    }
  };

  const handleToggleNotification = async (type: 'email' | 'sms' | 'app') => {
    if (!userData) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        Swal.fire({
          title: 'Error',
          text: 'Authentication token not found. Please log in again.',
          icon: 'error',
          confirmButtonColor: '#f59e0b',
        });
        return;
      }

      // Update local state immediately for better UX
      const updatedUserData = {
        ...userData,
        notifications: {
          ...userData.notifications,
          [type]: !userData.notifications[type]
        }
      };
      setUserData(updatedUserData);

      // In a real app, we would send the notification preferences to the server
      const response = await fetch('/api/dashboard/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          notifications: updatedUserData.notifications
        })
      });

      if (!response.ok) {
        // Revert the change if the request fails
        setUserData(userData);
        throw new Error(`Failed to update notification preferences: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        // Revert the change if the request fails
        setUserData(userData);
        throw new Error(result.message || 'Failed to update notification preferences');
      }
    } catch (err) {
      console.error('Error updating notification preferences:', err);
      Swal.fire({
        title: 'Error',
        text: err instanceof Error ? err.message : 'An error occurred while updating notification preferences',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
      });
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
        <p>{error}</p>
      </div>
    );
  }

  // No data state
  if (!userData) {
    return (
      <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
        <p>No settings data available. Please try again later.</p>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="space-y-6">
        {/* Profile Information */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Profile Information</h2>
            {!isEditingProfile && (
              <button
                onClick={() => {
                  setFormData(userData);
                  setIsEditingProfile(true);
                }}
                className="text-amber-600 hover:text-amber-700 font-medium"
              >
                Edit
              </button>
            )}
          </div>

          {isEditingProfile ? (
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="user.name"
                  value={formData.user.name}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="user.email"
                  value={formData.user.email}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="user.phone"
                  value={formData.user.phone}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div>
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                  Address
                </label>
                <input
                  type="text"
                  id="address"
                  name="user.address"
                  value={formData.user.address}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={handleSaveProfile}
                  className="px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600 transition"
                >
                  Save Changes
                </button>
                <button
                  onClick={() => setIsEditingProfile(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Full Name</p>
                <p className="font-medium">{userData.user.name}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Email Address</p>
                <p className="font-medium">{userData.user.email}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Phone Number</p>
                <p className="font-medium">{userData.user.phone || 'Not provided'}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Address</p>
                <p className="font-medium">{userData.user.address || 'Not provided'}</p>
              </div>
            </div>
          )}
        </div>

        {/* Emergency Contact */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Emergency Contact</h2>
            {!isEditingEmergency && (
              <button
                onClick={() => {
                  setFormData(userData);
                  setIsEditingEmergency(true);
                }}
                className="text-amber-600 hover:text-amber-700 font-medium"
              >
                Edit
              </button>
            )}
          </div>

          {isEditingEmergency ? (
            <div className="space-y-4">
              <div>
                <label htmlFor="emergencyContact.name" className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Name
                </label>
                <input
                  type="text"
                  id="emergencyContact.name"
                  name="emergencyContact.name"
                  value={formData.emergencyContact.name}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div>
                <label htmlFor="emergencyContact.relationship" className="block text-sm font-medium text-gray-700 mb-1">
                  Relationship
                </label>
                <input
                  type="text"
                  id="emergencyContact.relationship"
                  name="emergencyContact.relationship"
                  value={formData.emergencyContact.relationship}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div>
                <label htmlFor="emergencyContact.phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="emergencyContact.phone"
                  name="emergencyContact.phone"
                  value={formData.emergencyContact.phone}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={handleSaveEmergency}
                  className="px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600 transition"
                >
                  Save Changes
                </button>
                <button
                  onClick={() => setIsEditingEmergency(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-500">Contact Name</p>
                <p className="font-medium">{userData.emergencyContact.name}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Relationship</p>
                <p className="font-medium">{userData.emergencyContact.relationship}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Phone Number</p>
                <p className="font-medium">{userData.emergencyContact.phone}</p>
              </div>
            </div>
          )}
        </div>

        {/* Change Password */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Password</h2>
            {!isChangingPassword && (
              <button
                onClick={() => setIsChangingPassword(true)}
                className="text-amber-600 hover:text-amber-700 font-medium"
              >
                Change Password
              </button>
            )}
          </div>

          {isChangingPassword ? (
            <div className="space-y-4">
              <div>
                <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  Current Password
                </label>
                <input
                  type="password"
                  id="currentPassword"
                  name="currentPassword"
                  value={passwordData.currentPassword}
                  onChange={handlePasswordChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div>
                <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  New Password
                </label>
                <input
                  type="password"
                  id="newPassword"
                  name="newPassword"
                  value={passwordData.newPassword}
                  onChange={handlePasswordChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  Confirm New Password
                </label>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={passwordData.confirmPassword}
                  onChange={handlePasswordChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={handleSavePassword}
                  className="px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600 transition"
                >
                  Update Password
                </button>
                <button
                  onClick={() => setIsChangingPassword(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <p className="text-gray-600">
              Your password was last changed on <span className="font-medium">{new Date(userData.passwordLastChanged).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</span>.
            </p>
          )}
        </div>

        {/* Notification Settings */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold mb-6">Notification Settings</h2>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Email Notifications</p>
                <p className="text-sm text-gray-500">Receive notifications via email</p>
              </div>
              <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out rounded-full cursor-pointer">
                <input
                  type="checkbox"
                  id="email-toggle"
                  className="absolute w-6 h-6 opacity-0 cursor-pointer"
                  checked={userData.notifications.email}
                  onChange={() => handleToggleNotification('email')}
                />
                <label
                  htmlFor="email-toggle"
                  className={`block h-6 overflow-hidden rounded-full cursor-pointer ${
                    userData.notifications.email ? 'bg-amber-500' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`block h-6 w-6 rounded-full transform transition-transform duration-200 ease-in-out bg-white ${
                      userData.notifications.email ? 'translate-x-6' : 'translate-x-0'
                    }`}
                  ></span>
                </label>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">SMS Notifications</p>
                <p className="text-sm text-gray-500">Receive notifications via SMS</p>
              </div>
              <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out rounded-full cursor-pointer">
                <input
                  type="checkbox"
                  id="sms-toggle"
                  className="absolute w-6 h-6 opacity-0 cursor-pointer"
                  checked={userData.notifications.sms}
                  onChange={() => handleToggleNotification('sms')}
                />
                <label
                  htmlFor="sms-toggle"
                  className={`block h-6 overflow-hidden rounded-full cursor-pointer ${
                    userData.notifications.sms ? 'bg-amber-500' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`block h-6 w-6 rounded-full transform transition-transform duration-200 ease-in-out bg-white ${
                      userData.notifications.sms ? 'translate-x-6' : 'translate-x-0'
                    }`}
                  ></span>
                </label>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">App Notifications</p>
                <p className="text-sm text-gray-500">Receive in-app notifications</p>
              </div>
              <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out rounded-full cursor-pointer">
                <input
                  type="checkbox"
                  id="app-toggle"
                  className="absolute w-6 h-6 opacity-0 cursor-pointer"
                  checked={userData.notifications.app}
                  onChange={() => handleToggleNotification('app')}
                />
                <label
                  htmlFor="app-toggle"
                  className={`block h-6 overflow-hidden rounded-full cursor-pointer ${
                    userData.notifications.app ? 'bg-amber-500' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`block h-6 w-6 rounded-full transform transition-transform duration-200 ease-in-out bg-white ${
                      userData.notifications.app ? 'translate-x-6' : 'translate-x-0'
                    }`}
                  ></span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
