// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "./generated/client"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// User model
model User {
  id              String           @id @default(uuid())
  email           String           @unique
  password        String
  name            String
  role            UserRole         @default(VISITORS)
  phone           String?
  address         String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  rooms           Room[]           @relation("RoomToUser")
  payments        Payment[]
  contracts       Contract[]
  serviceRequests ServiceRequest[]
  serviceResponses ServiceResponse[]
  activities      Activity[]       @relation("UserActivities")
  actionsPerformed Activity[]      @relation("ActionPerformer")
  notifications   Notification[]
}

enum UserRole {
  ADMIN
  RESIDENT
  VISITORS
}

// Room Type model
model RoomType {
  id          String    @id @default(uuid())
  name        String    @unique
  description String?   @db.Text
  price       Float
  amenities   String?   @db.Text
  imageUrl    String?
  imageData   String?   @db.LongText
  fileType    String?
  fileName    String?
  rooms       Room[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// Room model
model Room {
  id                String    @id @default(uuid())
  roomNumber        String    @unique
  roomType          RoomType  @relation(fields: [roomTypeId], references: [id])
  roomTypeId        String
  monthlyRate       Float
  isOccupied        Boolean   @default(false)
  description       String?   @db.Text
  images            RoomImage[]
  residents         User[]    @relation("RoomToUser")
  payments          Payment[]
  contracts         Contract[]
  serviceRequests   ServiceRequest[]
  utilityReadings   UtilityReading[]
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}

// Room Image model
model RoomImage {
  id        String   @id @default(uuid())
  url       String?
  imageData String?  @db.LongText
  fileType  String?
  fileName  String?
  room      Room     @relation(fields: [roomId], references: [id], onDelete: Cascade)
  roomId    String
  createdAt DateTime @default(now())
}

// Payment model
model Payment {
  id              String        @id @default(uuid())
  amount          Float
  dueDate         DateTime
  paidDate        DateTime?
  status          PaymentStatus @default(PENDING)
  type            PaymentType
  notes           String?       @db.Text
  receiptNumber   String?
  receiptUrl      String?
  paymentMethod   String?
  user            User          @relation(fields: [userId], references: [id])
  userId          String
  room            Room          @relation(fields: [roomId], references: [id])
  roomId          String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
}

enum PaymentStatus {
  PAID
  PENDING
  UPCOMING
  OVERDUE
  CANCELLED
}

enum PaymentType {
  RENT
  UTILITIES
  DEPOSIT
  MAINTENANCE
  OTHER
}

// Service Request model
model ServiceRequest {
  id          String              @id @default(uuid())
  type        ServiceRequestType
  title       String
  description String              @db.Text
  status      ServiceRequestStatus @default(PENDING)
  priority    Priority            @default(MEDIUM)
  images      ServiceRequestImage[]
  user        User                @relation(fields: [userId], references: [id])
  userId      String
  room        Room                @relation(fields: [roomId], references: [id])
  roomId      String
  assignedTo  String?
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  resolvedAt  DateTime?
  responses   ServiceResponse[]
}

enum ServiceRequestType {
  MAINTENANCE
  CLEANING
  ELECTRICAL
  PLUMBING
  FURNITURE
  APPLIANCE
  INTERNET
  SECURITY
  OTHER
}

enum ServiceRequestStatus {
  PENDING
  IN_PROGRESS
  RESOLVED
  CANCELLED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// Service Request Image model
model ServiceRequestImage {
  id               String        @id @default(uuid())
  url              String
  serviceRequest   ServiceRequest @relation(fields: [serviceRequestId], references: [id], onDelete: Cascade)
  serviceRequestId String
  createdAt        DateTime      @default(now())
}

// Service Response model
model ServiceResponse {
  id               String         @id @default(uuid())
  message          String         @db.Text
  attachments      String?
  serviceRequest   ServiceRequest @relation(fields: [serviceRequestId], references: [id])
  serviceRequestId String
  user             User?          @relation(fields: [userId], references: [id])
  userId           String?
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
}

// Utility Reading model
model UtilityReading {
  id                    String    @id @default(uuid())
  room                  Room      @relation(fields: [roomId], references: [id])
  roomId                String
  waterReading          Float
  electricityReading    Float
  readingDate           DateTime  @default(now())
  waterUsage            Float?
  electricityUsage      Float?
  waterCost             Float?
  electricityCost       Float?
  totalCost             Float?
  isPaid                Boolean   @default(false)
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
}

// Activity Log model
model Activity {
  id          String        @id @default(uuid())
  user        User          @relation("UserActivities", fields: [userId], references: [id])
  userId      String
  performer   User?         @relation("ActionPerformer", fields: [performerId], references: [id])
  performerId String?
  action      String
  details     String?       @db.Text
  target      String?
  targetId    String?
  targetType  TargetType?
  ipAddress   String?
  timestamp   DateTime      @default(now())
}

enum TargetType {
  USER
  ROOM
  PAYMENT
  SERVICE_REQUEST
  UTILITY
  SYSTEM
}

// Notification model
model Notification {
  id          String              @id @default(uuid())
  user        User                @relation(fields: [userId], references: [id])
  userId      String
  title       String
  message     String              @db.Text
  type        NotificationType
  isRead      Boolean             @default(false)
  link        String?
  createdAt   DateTime            @default(now())
}

enum NotificationType {
  PAYMENT_DUE
  PAYMENT_RECEIVED
  SERVICE_REQUEST_UPDATE
  ANNOUNCEMENT
  SYSTEM
}


// Contract model
model Contract {
  id                String    @id @default(uuid())
  residentId        String
  resident          User      @relation(fields: [residentId], references: [id])
  roomId            String
  room              Room      @relation(fields: [roomId], references: [id])
  startDate         DateTime
  endDate           DateTime
  rentAmount        Float
  depositAmount     Float
  isActive          Boolean   @default(true)
  terminationDate   DateTime?
  terminationReason String?   @db.Text
  documentUrl       String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}
