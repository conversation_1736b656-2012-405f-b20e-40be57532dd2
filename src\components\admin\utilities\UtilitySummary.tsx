"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';
import DataTable from '@/components/admin/DataTable';

interface UtilitySummaryProps {
  rooms: Array<{
    id: string;
    roomNumber: string;
    roomType: {
      id: string;
      name: string;
    };
    waterUsage?: number;
    electricityUsage?: number;
    waterCost?: number;
    electricityCost?: number;
    totalCost?: number;
    lastUpdated?: string;
    status?: 'paid' | 'pending' | 'overdue';
  }>;
  isLoading?: boolean;
}

export default function UtilitySummary({ rooms, isLoading = false }: UtilitySummaryProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roomTypeFilter, setRoomTypeFilter] = useState('all');

  // Get unique room types for filter options
  const roomTypes = Array.from(new Set(rooms.map(room => room.roomType.name)));

  // Filter rooms based on search term, status filter, and room type
  const filteredRooms = rooms.filter(room => {
    const matchesSearch = room.roomNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || room.status === statusFilter;
    const matchesRoomType = roomTypeFilter === 'all' || room.roomType.name === roomTypeFilter;

    return matchesSearch && matchesStatus && matchesRoomType;
  });

  // Calculate totals
  const totalWaterUsage = filteredRooms.reduce((sum, room) => sum + (room.waterUsage || 0), 0);
  const totalElectricityUsage = filteredRooms.reduce((sum, room) => sum + (room.electricityUsage || 0), 0);
  const totalWaterCost = filteredRooms.reduce((sum, room) => sum + (room.waterCost || 0), 0);
  const totalElectricityCost = filteredRooms.reduce((sum, room) => sum + (room.electricityCost || 0), 0);
  const grandTotal = filteredRooms.reduce((sum, room) => sum + (room.totalCost || 0), 0);

  const columns = [
    {
      key: 'roomNumber',
      header: 'Room',
      render: (value: string, row: any) => (
        <div className="font-medium text-gray-900">
          {value} <span className="text-xs text-gray-500 ml-1">(Type {row.roomType.name})</span>
        </div>
      )
    },
    {
      key: 'roomType',
      header: 'Type',
      render: (value: any) => (
        <div className="text-center">
          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
            value.name === 'A' ? 'bg-blue-100 text-blue-800' :
            value.name === 'B' ? 'bg-purple-100 text-purple-800' :
            'bg-teal-100 text-teal-800'
          }`}>
            {value.name}
          </span>
        </div>
      )
    },
    {
      key: 'waterUsage',
      header: 'Water Usage',
      render: (value: number | undefined) => (
        <div>{value || 0} m³</div>
      )
    },
    {
      key: 'electricityUsage',
      header: 'Electricity Usage',
      render: (value: number | undefined) => (
        <div>{value || 0} kWh</div>
      )
    },
    {
      key: 'waterCost',
      header: 'Water Cost',
      render: (value: number | undefined) => (
        <div>฿{(value || 0).toFixed(2)}</div>
      )
    },
    {
      key: 'electricityCost',
      header: 'Electricity Cost',
      render: (value: number | undefined) => (
        <div>฿{(value || 0).toFixed(2)}</div>
      )
    },
    {
      key: 'totalCost',
      header: 'Total Cost',
      render: (value: number | undefined) => (
        <div className="font-bold text-amber-600">฿{(value || 0).toFixed(2)}</div>
      )
    },
    {
      key: 'status',
      header: 'Status',
      render: (value: string | undefined) => {
        const status = value || 'pending';
        return (
          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
            status === 'paid' ? 'bg-green-100 text-green-800' :
            status === 'pending' ? 'bg-amber-100 text-amber-800' :
            'bg-red-100 text-red-800'
          }`}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span>
        );
      }
    },
    {
      key: 'lastUpdated',
      header: 'Last Updated',
      render: (value: string | undefined) => (
        <div className="text-sm text-gray-500">
          {value ? new Date(value).toLocaleDateString() : 'Not recorded'}
        </div>
      )
    },
  ];

  return (
    <div className="space-y-6">
      {/* Search and Filter */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search by room number..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <span className="text-gray-500">Room Type:</span>
              <select
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                value={roomTypeFilter}
                onChange={(e) => setRoomTypeFilter(e.target.value)}
              >
                <option value="all">All Types</option>
                {roomTypes.map(type => (
                  <option key={type} value={type}>Type {type}</option>
                ))}
              </select>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-500">Status:</span>
              <select
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All</option>
                <option value="paid">Paid</option>
                <option value="pending">Pending</option>
                <option value="overdue">Overdue</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Water Usage</h3>
          <div className="flex justify-between items-end">
            <div className="text-2xl font-bold">{totalWaterUsage} m³</div>
            <div className="text-sm text-gray-500">฿{totalWaterCost.toFixed(2)}</div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Electricity Usage</h3>
          <div className="flex justify-between items-end">
            <div className="text-2xl font-bold">{totalElectricityUsage} kWh</div>
            <div className="text-sm text-gray-500">฿{totalElectricityCost.toFixed(2)}</div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Total Billing</h3>
          <div className="text-2xl font-bold text-amber-600">฿{grandTotal.toFixed(2)}</div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={filteredRooms}
        emptyMessage="No utility data available"
        isLoading={isLoading}
      />
    </div>
  );
}
