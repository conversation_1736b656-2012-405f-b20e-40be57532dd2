"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Tab } from '@headlessui/react';
import MeterReadingForm from '@/components/admin/utilities/MeterReadingForm';
import UtilitySummary from '@/components/admin/utilities/UtilitySummary';
import { getRooms } from '@/services/utilityService';
import { createUtilityReading, getUtilityReadingsByRoom } from '@/services/utilityService';
import { toast } from 'react-hot-toast';

// Get current month and year for tracking readings
const currentDate = new Date();
const currentMonth = currentDate.getMonth();
const currentYear = currentDate.getFullYear();
const currentMonthName = new Intl.DateTimeFormat('en-US', { month: 'long' }).format(currentDate);

// Interface for meter reading records
interface MeterReadingRecord {
  id: string;
  roomId: string;
  waterReading: number;
  electricityReading: number;
  timestamp: string;
  recordedBy: string;
}

// Interface for Room Type
interface RoomType {
  id: string;
  name: string;
}

// Interface for Room
interface Room {
  id: string;
  roomNumber: string;
  roomType: {
    id: string;
    name: string;
  };
  isOccupied: boolean;
  residents: {
    id: string;
    name: string;
    email: string;
  }[];
  // Additional properties for utility readings
  previousWaterReading?: number;
  currentWaterReading?: number;
  previousElectricityReading?: number;
  currentElectricityReading?: number;
  waterUsage?: number;
  electricityUsage?: number;
  waterCost?: number;
  electricityCost?: number;
  totalCost?: number;
  lastUpdated?: string;
  status?: 'paid' | 'pending' | 'overdue';
  lastReadingMonth?: number;
  lastReadingYear?: number;
  isCurrentMonthRecorded?: boolean;
}

// Interface for Utility Reading
interface UtilityReading {
  id: string;
  roomId: string;
  waterReading: number;
  electricityReading: number;
  readingDate: string;
  waterUsage: number | null;
  electricityUsage: number | null;
  waterCost: number | null;
  electricityCost: number | null;
  totalCost: number | null;
  isPaid: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function UtilitiesBilling() {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState(0);
  const [submittingRoomId, setSubmittingRoomId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [roomTypeFilter, setRoomTypeFilter] = useState('all');
  const [recordingStatusFilter, setRecordingStatusFilter] = useState('all');
  const [meterReadingRecords, setMeterReadingRecords] = useState<MeterReadingRecord[]>([]);
  const [roomTypes, setRoomTypes] = useState<{id: string, name: string}[]>([]);

  // Fetch rooms from the database
  useEffect(() => {
    const fetchRooms = async () => {
      setIsLoading(true);
      try {
        // Fetch rooms from the API
        const response = await getRooms();

        if (response.success) {
          // Process the rooms data to add utility reading information
          const processedRooms = await Promise.all(response.data.map(async (room: any) => {
            try {
              // Fetch utility readings for this room
              const readingsResponse = await getUtilityReadingsByRoom(room.id);

              if (readingsResponse.success && readingsResponse.data) {
                const { latestReading, previousReading } = readingsResponse.data;

                // Determine if current month is recorded
                const isCurrentMonthRecorded = latestReading ?
                  new Date(latestReading.readingDate).getMonth() === currentDate.getMonth() &&
                  new Date(latestReading.readingDate).getFullYear() === currentDate.getFullYear() :
                  false;

                return {
                  ...room,
                  previousWaterReading: previousReading ? previousReading.waterReading : 0,
                  currentWaterReading: latestReading ? latestReading.waterReading : 0,
                  previousElectricityReading: previousReading ? previousReading.electricityReading : 0,
                  currentElectricityReading: latestReading ? latestReading.electricityReading : 0,
                  waterUsage: latestReading ? latestReading.waterUsage : 0,
                  electricityUsage: latestReading ? latestReading.electricityUsage : 0,
                  waterCost: latestReading ? latestReading.waterCost : 0,
                  electricityCost: latestReading ? latestReading.electricityCost : 0,
                  totalCost: latestReading ? latestReading.totalCost : 0,
                  lastUpdated: latestReading ? new Date(latestReading.readingDate).toISOString().split('T')[0] : '',
                  status: latestReading ? (latestReading.isPaid ? 'paid' : 'pending') : 'pending',
                  lastReadingMonth: latestReading ? new Date(latestReading.readingDate).getMonth() : -1,
                  lastReadingYear: latestReading ? new Date(latestReading.readingDate).getFullYear() : -1,
                  isCurrentMonthRecorded
                };
              }

              // If no readings found, return room with default values
              return {
                ...room,
                previousWaterReading: 0,
                currentWaterReading: 0,
                previousElectricityReading: 0,
                currentElectricityReading: 0,
                waterUsage: 0,
                electricityUsage: 0,
                waterCost: 0,
                electricityCost: 0,
                totalCost: 0,
                lastUpdated: '',
                status: 'pending' as const,
                lastReadingMonth: -1,
                lastReadingYear: -1,
                isCurrentMonthRecorded: false
              };
            } catch (error) {
              console.error(`Error fetching readings for room ${room.id}:`, error);
              return room;
            }
          }));

          setRooms(processedRooms);

          // Extract unique room types
          const uniqueRoomTypes = Array.from(
            new Set(processedRooms.map((room: any) => room.roomType.name))
          ).map(name => {
            const room = processedRooms.find((r: any) => r.roomType.name === name);
            return {
              id: room.roomType.id,
              name: room.roomType.name
            };
          });

          setRoomTypes(uniqueRoomTypes);
        } else {
          console.error('Failed to fetch rooms:', response.message);
          toast.error('Failed to fetch rooms');
        }
      } catch (error) {
        console.error('Error fetching rooms:', error);
        toast.error('Error fetching rooms');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRooms();
  }, []);

  // Filter rooms based on search term, room type, and recording status
  const filteredRooms = rooms.filter((room: Room) => {
    const matchesSearch = room.roomNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRoomType = roomTypeFilter === 'all' || room.roomType.name === roomTypeFilter;

    // Check if the room matches the recording status filter
    let matchesRecordingStatus = true;
    if (recordingStatusFilter === 'recorded') {
      matchesRecordingStatus = room.isCurrentMonthRecorded || false;
    } else if (recordingStatusFilter === 'not-recorded') {
      matchesRecordingStatus = !room.isCurrentMonthRecorded;
    }

    return matchesSearch && matchesRoomType && matchesRecordingStatus;
  });

  const handleMeterReadingSubmit = async (data: { roomId: string; waterReading: number; electricityReading: number }) => {
    setSubmittingRoomId(data.roomId);

    try {
      // Find the room
      const room = rooms.find(r => r.id === data.roomId);

      if (!room) {
        toast.error('Room not found');
        setSubmittingRoomId(null);
        return;
      }

      // Create utility reading using the API
      const response = await createUtilityReading({
        roomId: data.roomId,
        waterReading: data.waterReading,
        electricityReading: data.electricityReading,
        readingDate: new Date().toISOString()
      });

      if (response.success) {
        toast.success('Meter reading recorded successfully');

        // Create a new meter reading record for local state
        const newRecord: MeterReadingRecord = {
          id: response.data.id,
          roomId: data.roomId,
          waterReading: data.waterReading,
          electricityReading: data.electricityReading,
          timestamp: new Date().toISOString(),
          recordedBy: 'Admin User' // In a real app, this would be the current user
        };

        // Add the new record to the records array
        setMeterReadingRecords(prevRecords => [...prevRecords, newRecord]);

        // Update the rooms state with the new reading
        const updatedRooms = rooms.map(r => {
          if (r.id === data.roomId) {
            const waterUsage = Math.max(0, data.waterReading - (r.previousWaterReading || 0));
            const electricityUsage = Math.max(0, data.electricityReading - (r.previousElectricityReading || 0));

            // Thailand utility rates
            const waterRate = 18; // ฿18 per cubic meter
            const electricityRate = 4; // ฿4 per kWh

            const waterCost = waterUsage * waterRate;
            const electricityCost = electricityUsage * electricityRate;

            // Get current date for recording
            const now = new Date();

            return {
              ...r,
              previousWaterReading: r.currentWaterReading,
              currentWaterReading: data.waterReading,
              previousElectricityReading: r.currentElectricityReading,
              currentElectricityReading: data.electricityReading,
              waterUsage,
              electricityUsage,
              waterCost,
              electricityCost,
              totalCost: waterCost + electricityCost,
              lastUpdated: now.toISOString().split('T')[0],
              status: 'pending' as const,
              lastReadingMonth: now.getMonth(),
              lastReadingYear: now.getFullYear(),
              isCurrentMonthRecorded: true
            };
          }
          return r;
        });

        setRooms(updatedRooms);
      } else {
        toast.error(response.message || 'Failed to record meter reading');
      }
    } catch (error) {
      console.error('Error recording meter reading:', error);
      toast.error('An error occurred while recording the meter reading');
    } finally {
      setSubmittingRoomId(null);
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Utilities Billing</h1>
        <p className="text-gray-600">Manage water and electricity billing for all rooms</p>
      </div>

      <Tab.Group selectedIndex={selectedTab} onChange={setSelectedTab}>
        <Tab.List className="flex space-x-1 rounded-xl bg-white p-1 shadow-sm mb-6">
          <Tab
            className={({ selected }) =>
              `w-full rounded-lg py-2.5 text-sm font-medium leading-5 transition-colors
              ${
                selected
                  ? 'bg-amber-500 text-white shadow'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`
            }
          >
            Record Meter Readings
          </Tab>
          <Tab
            className={({ selected }) =>
              `w-full rounded-lg py-2.5 text-sm font-medium leading-5 transition-colors
              ${
                selected
                  ? 'bg-amber-500 text-white shadow'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`
            }
          >
            View Billing Summary
          </Tab>
        </Tab.List>

        <Tab.Panels>
          {/* Record Meter Readings Panel */}
          <Tab.Panel>
            <div className="mb-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="relative flex-1">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    placeholder="Search by room number..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-500">Room Type:</span>
                    <select
                      className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                      value={roomTypeFilter}
                      onChange={(e) => setRoomTypeFilter(e.target.value)}
                    >
                      <option value="all">All Types</option>
                      {roomTypes.map(type => (
                        <option key={type.id} value={type.name}>Type {type.name}</option>
                      ))}
                    </select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <span className="text-gray-500">Recording Status:</span>
                    <select
                      className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                      value={recordingStatusFilter}
                      onChange={(e) => setRecordingStatusFilter(e.target.value)}
                    >
                      <option value="all">All Rooms</option>
                      <option value="recorded">Recorded for {currentMonthName}</option>
                      <option value="not-recorded">Not Yet Recorded</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {isLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 animate-pulse">
                    <div className="flex justify-between items-center mb-4">
                      <div className="h-6 bg-gray-200 rounded w-24"></div>
                      <div className="h-6 bg-gray-200 rounded w-32"></div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div className="space-y-4">
                        <div className="h-5 bg-gray-200 rounded w-32 mb-4"></div>
                        <div className="h-10 bg-gray-200 rounded"></div>
                        <div className="h-10 bg-gray-200 rounded"></div>
                      </div>
                      <div className="space-y-4">
                        <div className="h-5 bg-gray-200 rounded w-32 mb-4"></div>
                        <div className="h-10 bg-gray-200 rounded"></div>
                        <div className="h-10 bg-gray-200 rounded"></div>
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <div className="h-10 bg-gray-200 rounded w-32"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-6">
                {filteredRooms.length > 0 ? (
                  filteredRooms.map(room => (
                    <motion.div
                      key={room.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <MeterReadingForm
                        roomId={room.id}
                        roomNumber={room.roomNumber}
                        currentWaterReading={room.currentWaterReading}
                        currentElectricityReading={room.currentElectricityReading}
                        previousWaterReading={room.previousWaterReading}
                        previousElectricityReading={room.previousElectricityReading}
                        isCurrentMonthRecorded={room.isCurrentMonthRecorded}
                        lastReadingMonth={room.lastReadingMonth}
                        lastReadingYear={room.lastReadingYear}
                        onSubmit={handleMeterReadingSubmit}
                        isSubmitting={submittingRoomId === room.id}
                      />
                    </motion.div>
                  ))
                ) : (
                  <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 text-center text-gray-500">
                    No rooms found matching your search.
                  </div>
                )}
              </div>
            )}
          </Tab.Panel>

          {/* View Billing Summary Panel */}
          <Tab.Panel>
            <UtilitySummary rooms={rooms} isLoading={isLoading} />
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
}
