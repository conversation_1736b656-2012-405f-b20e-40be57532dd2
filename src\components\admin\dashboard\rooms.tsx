"use client";

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import DataTable from '@/components/admin/DataTable';
import Modal from '@/components/admin/Modal';
import AddRoomTypeModal from '@/components/admin/dashboard/AddRoomTypeModal';
import EditRoomTypeModal from '@/components/admin/dashboard/EditRoomTypeModal';
import AddRoomModal from '@/components/admin/dashboard/AddRoomModal';

// Mock data for room types
const initialRoomTypes = [
  { id: 1, name: 'Single Room', description: 'A room with a single bed', price: 6500, amenities: ['Air Conditioning', 'Private Bathroom', 'Study Desk', 'Wi-Fi'], imageUrl: 'single-room.jpg' },
  { id: 2, name: 'Double Room', description: 'A room with two single beds', price: 8000, amenities: ['Air Conditioning', 'Shared Bathroom', 'Study Desk', 'Wi-Fi'], imageUrl: 'double-room.jpg' },
  { id: 3, name: 'Suite', description: 'Premium accommodation with private bathroom and living area', price: 12000, amenities: ['Air Conditioning', 'Private Bathroom', 'Study Desk', 'Wi-Fi', 'Mini Fridge', 'TV'], imageUrl: 'suite.jpg' },
  { id: 4, name: 'Deluxe Room', description: 'Spacious room with premium amenities', price: 9500, amenities: ['Air Conditioning', 'Private Bathroom', 'Study Desk', 'Wi-Fi', 'Mini Fridge'], imageUrl: 'deluxe-room.jpg' },
];

// Mock data for individual rooms
const initialRooms = [
  // Single Rooms (A-101 to A-110)
  ...Array(10).fill(0).map((_, i) => ({
    id: `single-${i+1}`,
    roomNumber: `A-${101 + i}`,
    roomTypeId: 1,
    isOccupied: Math.random() > 0.7, // 30% chance of being occupied
    monthlyRate: 6500,
  })),

  // Double Rooms (B-201 to B-208)
  ...Array(8).fill(0).map((_, i) => ({
    id: `double-${i+1}`,
    roomNumber: `B-${201 + i}`,
    roomTypeId: 2,
    isOccupied: Math.random() > 0.6, // 40% chance of being occupied
    monthlyRate: 8000,
  })),

  // Suites (C-301 to C-305)
  ...Array(5).fill(0).map((_, i) => ({
    id: `suite-${i+1}`,
    roomNumber: `C-${301 + i}`,
    roomTypeId: 3,
    isOccupied: Math.random() > 0.5, // 50% chance of being occupied
    monthlyRate: 12000,
  })),

  // Deluxe Rooms (D-401 to D-407)
  ...Array(7).fill(0).map((_, i) => ({
    id: `deluxe-${i+1}`,
    roomNumber: `D-${401 + i}`,
    roomTypeId: 4,
    isOccupied: Math.random() > 0.7, // 30% chance of being occupied
    monthlyRate: 9500,
  })),
];

export default function RoomManagement() {
  const [roomTypes, setRoomTypes] = useState(initialRoomTypes);
  const [rooms, setRooms] = useState(initialRooms);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAddRoomModalOpen, setIsAddRoomModalOpen] = useState(false);
  const [isDeleteRoomModalOpen, setIsDeleteRoomModalOpen] = useState(false);
  const [currentRoomType, setCurrentRoomType] = useState<any>(null);
  const [currentRoom, setCurrentRoom] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    // Fetch room types from the API
    const fetchRoomTypes = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/room-types');
        const data = await response.json();

        if (data.success) {
          setRoomTypes(data.data);
          // Set the first room type as active by default
          if (data.data.length > 0) {
            setActiveTab(data.data[0].id);
          }
        } else {
          console.error('Failed to fetch room types:', data.message);
        }
      } catch (error) {
        console.error('Error fetching room types:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Fetch rooms from the API
    const fetchRooms = async () => {
      try {
        const response = await fetch('/api/rooms');
        const data = await response.json();

        if (data.success) {
          setRooms(data.data);
        } else {
          console.error('Failed to fetch rooms:', data.message);
        }
      } catch (error) {
        console.error('Error fetching rooms:', error);
      }
    };

    fetchRoomTypes();
    fetchRooms();
  }, []);

  // Get rooms for a specific room type
  const getRoomsByType = (roomTypeId: number) => {
    return rooms.filter(room => room.roomTypeId === roomTypeId);
  };

  // Count available and occupied rooms by type
  const getRoomCountsByType = (roomTypeId: number) => {
    const roomsOfType = getRoomsByType(roomTypeId);
    const available = roomsOfType.filter(room => !room.isOccupied).length;
    const occupied = roomsOfType.filter(room => room.isOccupied).length;
    return {
      total: roomsOfType.length,
      available,
      occupied
    };
  };

  // Filter room types based on search term
  const filteredRoomTypes = roomTypes.filter(room =>
    room.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    room.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const openAddModal = () => {
    setIsAddModalOpen(true);
  };

  const openEditModal = (roomType: any) => {
    setCurrentRoomType(roomType);
    setIsEditModalOpen(true);
  };

  const openDeleteModal = (roomType: any) => {
    setCurrentRoomType(roomType);
    setIsDeleteModalOpen(true);
  };

  const handleAddRoomType = async (formData: any) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('You must be logged in to add a room type');
        return;
      }

      const response = await fetch('/api/room-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        // Add the new room type to the state
        setRoomTypes([...roomTypes, data.data]);
        setIsAddModalOpen(false);
      } else {
        alert(`Failed to add room type: ${data.message}`);
      }
    } catch (error) {
      console.error('Error adding room type:', error);
      alert('An error occurred while adding the room type');
    }
  };

  const handleEditRoomType = async (updatedRoomType: any) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('You must be logged in to edit a room type');
        return;
      }

      const response = await fetch(`/api/room-types/${updatedRoomType.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updatedRoomType)
      });

      const data = await response.json();

      if (data.success) {
        // Update the room type in the state
        const updatedRoomTypes = roomTypes.map(roomType =>
          roomType.id === updatedRoomType.id ? data.data : roomType
        );
        setRoomTypes(updatedRoomTypes);
        setIsEditModalOpen(false);
      } else {
        alert(`Failed to update room type: ${data.message}`);
      }
    } catch (error) {
      console.error('Error updating room type:', error);
      alert('An error occurred while updating the room type');
    }
  };

  const handleDeleteRoomType = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('You must be logged in to delete a room type');
        return;
      }

      const response = await fetch(`/api/room-types/${currentRoomType.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        // Remove the room type from the state
        const updatedRoomTypes = roomTypes.filter(roomType => roomType.id !== currentRoomType.id);
        setRoomTypes(updatedRoomTypes);
        setIsDeleteModalOpen(false);
      } else {
        alert(`Failed to delete room type: ${data.message}`);
      }
    } catch (error) {
      console.error('Error deleting room type:', error);
      alert('An error occurred while deleting the room type');
    }
  };

  const openAddRoomModal = (roomType: any) => {
    setCurrentRoomType(roomType);
    setIsAddRoomModalOpen(true);
  };

  const handleAddRoom = async (newRoom: any) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('You must be logged in to add a room');
        return;
      }

      const response = await fetch('/api/rooms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(newRoom)
      });

      const data = await response.json();

      if (data.success) {
        // Add the new room to the state
        setRooms([...rooms, data.data]);
        setIsAddRoomModalOpen(false);
      } else {
        alert(`Failed to add room: ${data.message}`);
      }
    } catch (error) {
      console.error('Error adding room:', error);
      alert('An error occurred while adding the room');
    }
  };

  const openDeleteRoomModal = (room: any) => {
    setCurrentRoom(room);
    setIsDeleteRoomModalOpen(true);
  };

  const handleDeleteRoom = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('You must be logged in to delete a room');
        return;
      }

      const response = await fetch(`/api/rooms/${currentRoom.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        // Remove the room from the state
        const updatedRooms = rooms.filter(room => room.id !== currentRoom.id);
        setRooms(updatedRooms);
        setIsDeleteRoomModalOpen(false);
      } else {
        alert(`Failed to delete room: ${data.message}`);
      }
    } catch (error) {
      console.error('Error deleting room:', error);
      alert('An error occurred while deleting the room');
    }
  };

  const amenitiesList = [
    'Wi-Fi',
    'Air Conditioning',
    'Private Bathroom',
    'Shared Bathroom',
    'Study Desk',
    'Mini Fridge',
    'TV',
    'Wardrobe',
    'Balcony',
    'Kitchenette'
  ];

  const roomTypeColumns = [
    {
      key: 'name',
      header: 'Room Type',
      render: (value: string) => (
        <div className="text-sm font-medium text-gray-900">{value}</div>
      )
    },
    {
      key: 'description',
      header: 'Description',
      render: (value: string) => (
        <div className="text-sm text-gray-500 max-w-xs truncate">{value}</div>
      )
    },
    {
      key: 'price',
      header: 'Price',
      render: (value: number) => (
        <div className="text-sm text-gray-900">฿{value.toLocaleString()}/month</div>
      )
    },
    {
      key: 'roomCount',
      header: 'Room Count',
      render: (_: any, row: any) => {
        const counts = getRoomCountsByType(row.id);
        return (
          <div className="text-sm text-gray-900">
            {counts.total} ({counts.available} available)
          </div>
        );
      }
    },
    {
      key: 'amenities',
      header: 'Amenities',
      render: (value: string[]) => (
        <div className="flex flex-wrap gap-1">
          {value.slice(0, 2).map((amenity, index) => (
            <span key={index} className="px-2 py-1 text-xs bg-gray-100 rounded-full text-gray-800">
              {amenity}
            </span>
          ))}
          {value.length > 2 && (
            <span className="px-2 py-1 text-xs bg-gray-100 rounded-full text-gray-800">
              +{value.length - 2} more
            </span>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_: any, row: any) => (
        <div className="text-right">
          <button
            onClick={(e) => {
              e.stopPropagation();
              openEditModal(row);
            }}
            className="text-amber-600 hover:text-amber-900 mr-4"
          >
            Edit
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              openDeleteModal(row);
            }}
            className="text-red-600 hover:text-red-900"
          >
            Delete
          </button>
        </div>
      )
    },
  ];

  // Columns for individual rooms table
  const roomColumns = [
    {
      key: 'roomNumber',
      header: 'Room Number',
      render: (value: string) => (
        <div className="text-sm font-medium text-gray-900">{value}</div>
      )
    },
    {
      key: 'monthlyRate',
      header: 'Monthly Rate',
      render: (value: number) => (
        <div className="text-sm text-gray-900">฿{value.toLocaleString()}</div>
      )
    },
    {
      key: 'isOccupied',
      header: 'Status',
      render: (value: boolean) => (
        <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          value ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
        }`}>
          {value ? 'Occupied' : 'Available'}
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_: any, room: any) => (
        <div className="text-right">
          <button
            onClick={(e) => {
              e.stopPropagation();
              // Handle room details view
              alert(`View details for room ${room.roomNumber}`);
            }}
            className="text-amber-600 hover:text-amber-900 mr-4"
          >
            Details
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              openDeleteRoomModal(room);
            }}
            className="text-red-600 hover:text-red-900"
          >
            Delete
          </button>
        </div>
      )
    },
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Room Management</h1>
          <p className="text-gray-600">Manage room types and their details</p>
        </div>
        <button
          onClick={openAddModal}
          className="bg-amber-500 hover:bg-amber-600 text-white px-4 py-2 rounded-lg flex items-center transition"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Room Type
        </button>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search room types..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-gray-500">Sort by:</span>
            <select className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent">
              <option value="name">Name</option>
              <option value="price-asc">Price (Low to High)</option>
              <option value="price-desc">Price (High to Low)</option>
              <option value="capacity">Capacity</option>
            </select>
          </div>
        </div>
      </div>

      {/* Room Types Overview */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Room Types Overview</h2>
        <DataTable
          columns={roomTypeColumns}
          data={filteredRoomTypes}
          emptyMessage="No room types found"
          isLoading={isLoading}
          onRowClick={(row) => {
            setActiveTab(row.id);
          }}
        />
      </div>

      {/* Room Details by Type */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Room Details</h2>

        {/* Tabs for Room Types */}
        <div className="border-b border-gray-200 mb-4">
          <nav className="-mb-px flex space-x-8">
            {roomTypes.map((type) => {
              const counts = getRoomCountsByType(type.id);
              return (
                <button
                  key={type.id}
                  onClick={() => setActiveTab(type.id)}
                  className={`
                    whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
                    ${activeTab === type.id
                      ? 'border-amber-500 text-amber-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}
                  `}
                >
                  {type.name}
                  <span className="ml-2 bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
                    {counts.total}
                  </span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Room Table for Selected Type */}
        {activeTab && (
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6">
                {getRoomsByType(activeTab).map((room) => (
                  <div
                    key={room.id}
                    className={`
                      p-4 rounded-lg border ${room.isOccupied
                        ? 'border-red-200 bg-red-50'
                        : 'border-green-200 bg-green-50'
                      }
                    `}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-lg font-medium">{room.roomNumber}</span>
                      <span className={`
                        inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        ${room.isOccupied ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}
                      `}>
                        {room.isOccupied ? 'Occupied' : 'Available'}
                      </span>
                    </div>
                    <div className="text-sm font-medium mt-2">฿{room.monthlyRate.toLocaleString()}/month</div>
                  </div>
                ))}
              </div>

              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-800">
                  {roomTypes.find(type => type.id === activeTab)?.name} Rooms
                </h3>
                <button
                  onClick={() => openAddRoomModal(roomTypes.find(type => type.id === activeTab))}
                  className="bg-amber-500 hover:bg-amber-600 text-white px-3 py-1 rounded-lg text-sm flex items-center transition"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Room
                </button>
              </div>

              <DataTable
                columns={roomColumns}
                data={getRoomsByType(activeTab)}
                emptyMessage={`No ${roomTypes.find(type => type.id === activeTab)?.name} rooms found`}
                isLoading={isLoading}
              />
            </motion.div>
          </AnimatePresence>
        )}
      </div>

      {/* Add Room Type Modal */}
      <AddRoomTypeModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAddRoomType={handleAddRoomType}
        amenitiesList={amenitiesList}
      />

      {/* Edit Room Type Modal */}
      <EditRoomTypeModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onEditRoomType={handleEditRoomType}
        currentRoomType={currentRoomType}
        amenitiesList={amenitiesList}
      />

      {/* Add Room Modal */}
      {currentRoomType && (
        <AddRoomModal
          isOpen={isAddRoomModalOpen}
          onClose={() => setIsAddRoomModalOpen(false)}
          onAddRoom={handleAddRoom}
          roomTypeId={currentRoomType.id}
          roomTypeName={currentRoomType.name}
          defaultMonthlyRate={currentRoomType.price}
        />
      )}

      {/* Delete Room Type Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Room Type"
        footer={
          <>
            <button
              type="button"
              onClick={handleDeleteRoomType}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Delete
            </button>
            <button
              type="button"
              onClick={() => setIsDeleteModalOpen(false)}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </>
        }
      >
        {currentRoomType && (
          <div>
            <div className="sm:flex sm:items-start">
              <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete the "{currentRoomType.name}" room type? This action cannot be undone.
                </p>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete Room Confirmation Modal */}
      <Modal
        isOpen={isDeleteRoomModalOpen}
        onClose={() => setIsDeleteRoomModalOpen(false)}
        title="Delete Room"
        footer={
          <>
            <button
              type="button"
              onClick={handleDeleteRoom}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Delete
            </button>
            <button
              type="button"
              onClick={() => setIsDeleteRoomModalOpen(false)}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </>
        }
      >
        {currentRoom && (
          <div>
            <div className="sm:flex sm:items-start">
              <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete room "{currentRoom.roomNumber}"? This action cannot be undone.
                </p>
                {currentRoom.isOccupied && (
                  <p className="mt-2 text-sm text-red-500 font-medium">
                    Warning: This room is currently occupied. Deleting it may cause issues with resident records.
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}
