import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON> } from 'next/font/google';
import { Source_Code_Pro } from 'next/font/google';

const GeistSans = Roboto({ subsets: ['latin'], variable: '--font-roboto' });
const GeistMono = Source_Code_Pro({ subsets: ['latin'], variable: '--font-source-code-pro' });
import './globals.css';

import Navbar from '@/components/Navbar';
import { AuthProvider } from '@/context/AuthContext';

export const metadata: Metadata = {
  title: 'Campus Dormitory',
  description: 'Find your perfect student accommodation',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${GeistSans.variable} ${GeistMono.variable}`}>
      <body>
        <AuthProvider>
          <Navbar />
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
