"use client";

import Link from 'next/link';
import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { user, isLoading, logout } = useAuth();

  const handleLogout = () => {
    // Close user menu
    setIsUserMenuOpen(false);
    // Call logout from auth context
    logout();
  };

  return (
    <nav className="w-full bg-white py-3 px-4 md:px-6 flex justify-between items-center shadow-md relative">
      {/* Logo/Brand */}
      <Link href="/" className="text-xl font-bold text-[#3c2415]">
        Suk somboon
      </Link>

      {/* Mobile Menu Button */}
      <button
        className="md:hidden"
        onClick={() => setIsMenuOpen(!isMenuOpen)}
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-6 h-6">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>

      {/* Desktop Navigation */}
      <div className="hidden md:flex items-center space-x-6">
        <Link href="/" className="text-gray-700 hover:text-gray-900">
          หน้าหลัก
        </Link>
        <Link href="/rooms" className="text-gray-700 hover:text-gray-900">
          ห้องว่าง
        </Link>
        <Link href="/contact" className="text-gray-700 hover:text-gray-900">
          ติดต่อ
        </Link>

        {/* Search Icon */}
        <button className="text-gray-700 hover:text-gray-900">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>

        {/* User Section */}
        {!isLoading && (
          <>
            {user ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-5 h-5">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <span className="font-medium">{user.name}</span>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-4 h-4">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* User Dropdown Menu */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 ">
                    {user.role === 'ADMIN' && (
                      <Link
                        href="/admin/dashboard"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        Admin Dashboard
                      </Link>
                    )}
                    <Link
                      href="/user/dashboard"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      User Dashboard
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                    >
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Link
                href="/auth"
                className="bg-[#3c2415] text-white px-4 py-2 rounded-md hover:bg-[#2a1a0f] transition"
              >
                สมัครสมาชิก
              </Link>
            )}
          </>
        )}
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="absolute top-16 left-0 right-0 bg-white shadow-md p-4 md:hidden z-50">
          <div className="flex flex-col space-y-4">
            <Link href="/" className="text-gray-700 hover:text-gray-900">
              หน้าหลัก
            </Link>
            <Link href="/rooms" className="text-gray-700 hover:text-gray-900">
              ห้องว่าง
            </Link>
            <Link href="/contact" className="text-gray-700 hover:text-gray-900">
              ติดต่อ
            </Link>
            <div className="flex justify-between items-center pt-2 border-t">
              {!isLoading && (
                <>
                  {user ? (
                    <div className="w-full">
                      <div className="flex items-center mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-5 h-5 mr-2">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <span className="font-medium">{user.name}</span>
                      </div>
                      <div className="flex flex-col space-y-2">
                        {user.role === 'ADMIN' && (
                          <Link
                            href="/admin/dashboard"
                            className="text-gray-700 hover:text-gray-900 pl-7"
                            onClick={() => setIsMenuOpen(false)}
                          >
                            Admin Dashboard
                          </Link>
                        )}
                        <Link
                          href="/user/dashboard"
                          className="text-gray-700 hover:text-gray-900 pl-7"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          User Dashboard
                        </Link>
                        <Link
                          href="/user/profile"
                          className="text-gray-700 hover:text-gray-900 pl-7"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          Profile
                        </Link>
                        <button
                          onClick={handleLogout}
                          className="text-left text-red-600 hover:text-red-800 pl-7"
                        >
                          Logout
                        </button>
                      </div>
                    </div>
                  ) : (
                    <>
                      <Link href="/user/dashboard" className="text-gray-700 hover:text-gray-900 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-5 h-5 mr-2">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        แดชบอร์ด
                      </Link>
                      <Link
                        href="/auth"
                        className="bg-[#3c2415] text-white px-4 py-2 rounded-md hover:bg-[#2a1a0f] transition"
                      >
                        Sign up
                      </Link>
                    </>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}
