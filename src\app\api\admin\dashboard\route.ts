import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

// GET dashboard data for admin
async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized - Admin role required' },
        { status: 403 }
      );
    }

    // Get room statistics
    const [
      totalRooms,
      occupiedRooms,
      // Get payment statistics
      pendingPayments,
      // Get recent activities
      recentActivities,
      // Get pending service requests
      pendingServiceRequests,
      // Get upcoming payments
      upcomingPayments
    ] = await Promise.all([
      // Total rooms count
      prisma.room.count(),
      
      // Occupied rooms count
      prisma.room.count({
        where: { isOccupied: true }
      }),
      
      // Pending payments count
      prisma.payment.count({
        where: { status: 'PENDING' }
      }),
      
      // Recent activities
      prisma.activity.findMany({
        take: 5,
        orderBy: { timestamp: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              role: true
            }
          },
          performer: {
            select: {
              id: true,
              name: true,
              role: true
            }
          }
        }
      }),
      
      // Pending service requests
      prisma.serviceRequest.findMany({
        where: {
          status: {
            in: ['PENDING', 'IN_PROGRESS']
          }
        },
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              name: true
            }
          },
          room: {
            select: {
              id: true,
              roomNumber: true
            }
          }
        }
      }),
      
      // Upcoming payments
      prisma.payment.findMany({
        where: {
          status: 'PENDING',
          dueDate: {
            gte: new Date()
          }
        },
        take: 5,
        orderBy: { dueDate: 'asc' },
        include: {
          user: {
            select: {
              id: true,
              name: true
            }
          },
          room: {
            select: {
              id: true,
              roomNumber: true
            }
          }
        }
      })
    ]);

    // Calculate vacant rooms
    const vacantRooms = totalRooms - occupiedRooms;

    // Get previous month's room statistics for comparison
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    
    const previousMonthStats = await prisma.activity.findMany({
      where: {
        timestamp: {
          gte: oneMonthAgo
        },
        action: {
          in: ['assigned room', 'checked in', 'checked out']
        }
      }
    });

    // Calculate changes for stats
    const roomAssignments = previousMonthStats.filter(a => 
      a.action === 'assigned room' || a.action === 'checked in'
    ).length;
    
    const roomCheckouts = previousMonthStats.filter(a => 
      a.action === 'checked out'
    ).length;

    // Format activities for response
    const formattedActivities = recentActivities.map(activity => {
      // Find the room information from the activity details or target
      let room = '';
      if (activity.details && activity.details.includes('Room')) {
        const roomMatch = activity.details.match(/Room ([A-Z]-\d+)/);
        if (roomMatch) room = roomMatch[1];
      }

      // Calculate relative time
      const activityTime = new Date(activity.timestamp);
      const now = new Date();
      const diffMs = now.getTime() - activityTime.getTime();
      const diffMins = Math.round(diffMs / 60000);
      const diffHours = Math.round(diffMs / 3600000);
      const diffDays = Math.round(diffMs / 86400000);
      
      let timeAgo;
      if (diffMins < 60) {
        timeAgo = `${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'} ago`;
      } else if (diffHours < 24) {
        timeAgo = `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
      } else {
        timeAgo = `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
      }

      // Extract amount from details if it exists
      let amount;
      if (activity.details && activity.details.includes('฿')) {
        const amountMatch = activity.details.match(/฿([\d,]+)/);
        if (amountMatch) amount = `฿${amountMatch[1]}`;
      }

      return {
        id: activity.id,
        user: activity.user.name,
        action: activity.action,
        amount: amount,
        details: activity.details ? activity.details.replace(/฿[\d,]+/, '').replace(/Room [A-Z]-\d+/, '') : undefined,
        time: timeAgo,
        room: room || 'N/A'
      };
    });

    // Format service requests for response
    const formattedServiceRequests = pendingServiceRequests.map(request => ({
      id: request.id,
      type: request.type,
      user: request.user.name,
      room: request.room.roomNumber,
      status: request.status === 'IN_PROGRESS' ? 'In Progress' : 'Pending',
      date: request.createdAt.toISOString().split('T')[0],
      priority: request.priority
    }));

    // Format upcoming payments for response
    const formattedPayments = upcomingPayments.map(payment => ({
      id: payment.id,
      user: payment.user.name,
      room: payment.room.roomNumber,
      amount: payment.amount,
      dueDate: payment.dueDate.toISOString().split('T')[0],
      status: 'Pending'
    }));

    // Construct the response
    const dashboardData = {
      stats: [
        {
          title: "Total Rooms",
          value: totalRooms,
          change: { value: roomAssignments, isPositive: roomAssignments > 0 },
        },
        {
          title: "Occupied Rooms",
          value: occupiedRooms,
          change: { value: roomAssignments - roomCheckouts, isPositive: roomAssignments > roomCheckouts },
        },
        {
          title: "Vacant Rooms",
          value: vacantRooms,
          change: { value: roomCheckouts - roomAssignments, isPositive: roomCheckouts > roomAssignments },
        },
        {
          title: "Pending Payments",
          value: pendingPayments,
          change: { value: 0, isPositive: false }, // This would need historical data to calculate
        },
      ],
      recentActivities: formattedActivities,
      pendingRequests: formattedServiceRequests,
      upcomingPayments: formattedPayments
    };

    return NextResponse.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    console.error('Error fetching admin dashboard data:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedGET = withAuth(GET, ['ADMIN']);

export { protectedGET as GET };
