import { PrismaClient } from './generated/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 10);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      password: adminPassword,
      role: 'ADMIN',
    },
  });

  console.log('Admin user created:', admin);

  // Create room type
  const roomType = await prisma.roomType.upsert({
    where: { name: 'Standard Room' },
    update: {},
    create: {
      name: 'Standard Room',
      description: 'A comfortable standard room with basic amenities',
      price: 5000,
      amenities: 'Air conditioning, Wi-Fi, TV, Refrigerator',
    },
  });

  console.log('Room type created:', roomType);

  // Create room
  const room = await prisma.room.upsert({
    where: { roomNumber: 'A-101' },
    update: {},
    create: {
      roomNumber: 'A-101',
      roomTypeId: roomType.id,
      monthlyRate: 5000,
      description: 'First floor standard room',
    },
  });

  console.log('Room created:', room);

  // Create resident user
  const residentPassword = await bcrypt.hash('resident123', 10);
  const resident = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Resident User',
      password: residentPassword,
      role: 'RESIDENT',
    },
  });

  console.log('Resident user created:', resident);

  // Create contract
  const startDate = new Date(2023, 0, 1); // January 1, 2023
  const endDate = new Date(2024, 11, 31); // December 31, 2024

  const contract = await prisma.contract.upsert({
    where: {
      id: 'contract-1',
    },
    update: {},
    create: {
      id: 'contract-1',
      residentId: resident.id,
      roomId: room.id,
      startDate,
      endDate,
      rentAmount: 5000,
      depositAmount: 10000,
      isActive: true,
    },
  });

  console.log('Contract created:', contract);
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
