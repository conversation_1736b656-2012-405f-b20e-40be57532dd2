"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';

interface RoomFilterProps {
  onFilterChange: (filters: {
    type: string;
    capacity: string;
    priceRange: [number, number];
  }) => void;
  roomTypes: string[];
  maxPrice: number;
}

export default function RoomFilter({ onFilterChange, roomTypes, maxPrice }: RoomFilterProps) {
  const [type, setType] = useState('all');
  const [capacity, setCapacity] = useState('all');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, maxPrice]);
  const [isExpanded, setIsExpanded] = useState(false);

  const handleTypeChange = (newType: string) => {
    setType(newType);
    onFilterChange({
      type: newType,
      capacity,
      priceRange,
    });
  };

  const handleCapacityChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newCapacity = e.target.value;
    setCapacity(newCapacity);
    onFilterChange({
      type,
      capacity: newCapacity,
      priceRange,
    });
  };

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPrice = parseInt(e.target.value);
    const newPriceRange: [number, number] = [0, newPrice];
    setPriceRange(newPriceRange);
    onFilterChange({
      type,
      capacity,
      priceRange: newPriceRange,
    });
  };

  const handleReset = () => {
    setType('all');
    setCapacity('all');
    setPriceRange([0, maxPrice]);
    onFilterChange({
      type: 'all',
      capacity: 'all',
      priceRange: [0, maxPrice],
    });
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 mb-8 overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-bold text-gray-900">Filter Rooms</h2>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-amber-500 hover:text-amber-600 text-sm font-medium md:hidden"
          >
            {isExpanded ? 'Hide Filters' : 'Show Filters'}
          </button>
        </div>

        <div className={`${isExpanded ? 'block' : 'hidden'} md:block`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Room Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Room Type</label>
              <div className="flex flex-wrap gap-2">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleTypeChange('all')}
                  className={`px-3 py-1 rounded-full text-sm ${
                    type === 'all'
                      ? 'bg-amber-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  All
                </motion.button>
                {roomTypes.map((roomType) => (
                  <motion.button
                    key={roomType}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleTypeChange(roomType)}
                    className={`px-3 py-1 rounded-full text-sm ${
                      type === roomType
                        ? 'bg-amber-500 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {roomType}
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Capacity Filter */}
            <div>
              <label htmlFor="capacity" className="block text-sm font-medium text-gray-700 mb-2">
                Capacity
              </label>
              <select
                id="capacity"
                value={capacity}
                onChange={handleCapacityChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              >
                <option value="all">Any capacity</option>
                <option value="1">1 Person</option>
                <option value="2">2 People</option>
                <option value="3">3+ People</option>
              </select>
            </div>

            {/* Price Range Filter */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                  Max Price
                </label>
                <span className="text-amber-600 font-medium">${priceRange[1]}</span>
              </div>
              <input
                type="range"
                id="price"
                min="0"
                max={maxPrice}
                value={priceRange[1]}
                onChange={handlePriceChange}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-amber-500"
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleReset}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 text-sm font-medium"
            >
              Reset Filters
            </motion.button>
          </div>
        </div>
      </div>
    </div>
  );
}
