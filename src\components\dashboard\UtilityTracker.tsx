"use client";

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface UtilityTrackerProps {
  type: 'water' | 'electricity';
  current: number;
  previous: number;
  rate: number;
  compact?: boolean;
}

export default function UtilityTracker({ type, current, previous, rate, compact = false }: UtilityTrackerProps) {
  const [usage, setUsage] = useState(0);
  const [cost, setCost] = useState(0);
  const [percentage, setPercentage] = useState(0);
  
  useEffect(() => {
    const usageAmount = current - previous;
    setUsage(usageAmount);
    
    const costAmount = usageAmount * rate;
    setCost(costAmount);
    
    // Calculate percentage increase/decrease
    // For demo purposes, we'll use a baseline of 10 units for water and 100 units for electricity
    const baseline = type === 'water' ? 10 : 100;
    const percentChange = ((usageAmount - baseline) / baseline) * 100;
    setPercentage(percentChange);
  }, [current, previous, rate, type]);

  const getUtilityIcon = () => {
    if (type === 'water') {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
      );
    } else {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      );
    }
  };

  const getUtilityColor = () => {
    if (type === 'water') {
      return 'text-blue-500';
    } else {
      return 'text-yellow-500';
    }
  };

  const getUtilityUnit = () => {
    if (type === 'water') {
      return 'cubic meters';
    } else {
      return 'kWh';
    }
  };

  const getUtilityTitle = () => {
    if (type === 'water') {
      return 'Water Usage';
    } else {
      return 'Electricity Usage';
    }
  };

  if (compact) {
    return (
      <div className="flex items-center space-x-4">
        <div className={`p-3 rounded-full ${type === 'water' ? 'bg-blue-100' : 'bg-yellow-100'}`}>
          <div className={getUtilityColor()}>
            {getUtilityIcon()}
          </div>
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-medium text-gray-900">{getUtilityTitle()}</h3>
          <div className="flex items-center mt-1">
            <div className="text-2xl font-bold">{usage}</div>
            <div className="ml-1 text-sm text-gray-500">{type === 'water' ? 'm³' : 'kWh'}</div>
            <div className={`ml-2 text-sm ${percentage > 0 ? 'text-red-500' : 'text-green-500'}`}>
              {percentage > 0 ? '↑' : '↓'} {Math.abs(Math.round(percentage))}%
            </div>
          </div>
          <div className="mt-1 text-sm text-gray-500">
            ฿{cost.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <div className={`p-4 rounded-full ${type === 'water' ? 'bg-blue-100' : 'bg-yellow-100'} mr-4`}>
          <div className={getUtilityColor()}>
            {getUtilityIcon()}
          </div>
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">{getUtilityTitle()}</h3>
          <p className="text-sm text-gray-500">Current billing period</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-50 p-4 rounded-lg">
          <p className="text-sm text-gray-500 mb-1">Current Reading</p>
          <p className="text-2xl font-bold">{current} <span className="text-sm font-normal text-gray-500">{type === 'water' ? 'm³' : 'kWh'}</span></p>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <p className="text-sm text-gray-500 mb-1">Previous Reading</p>
          <p className="text-2xl font-bold">{previous} <span className="text-sm font-normal text-gray-500">{type === 'water' ? 'm³' : 'kWh'}</span></p>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <p className="text-sm text-gray-500 mb-1">Usage</p>
          <div className="flex items-center">
            <p className="text-2xl font-bold">{usage} <span className="text-sm font-normal text-gray-500">{type === 'water' ? 'm³' : 'kWh'}</span></p>
            <div className={`ml-2 text-sm ${percentage > 0 ? 'text-red-500' : 'text-green-500'}`}>
              {percentage > 0 ? '↑' : '↓'} {Math.abs(Math.round(percentage))}%
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex justify-between mb-2">
          <h4 className="font-medium">Usage Breakdown</h4>
          <p className="text-gray-500">Rate: ฿{rate}/{type === 'water' ? 'm³' : 'kWh'}</p>
        </div>
        
        <div className="mt-4 space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Usage ({usage} {type === 'water' ? 'm³' : 'kWh'})</span>
              <span>฿{(usage * rate).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
            </div>
            <div className="w-full bg-gray-100 rounded-full h-2.5">
              <motion.div 
                className={`h-2.5 rounded-full ${type === 'water' ? 'bg-blue-500' : 'bg-yellow-500'}`}
                initial={{ width: 0 }}
                animate={{ width: '100%' }}
                transition={{ duration: 1 }}
              />
            </div>
          </div>
          
          <div className="pt-4 border-t border-gray-100">
            <div className="flex justify-between">
              <span className="font-medium">Total</span>
              <span className="font-bold">฿{cost.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h4 className="font-medium mb-4">Conservation Tips</h4>
        <ul className="space-y-2 text-sm text-gray-600">
          {type === 'water' ? (
            <>
              <li className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Fix leaky faucets and toilets promptly
              </li>
              <li className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Take shorter showers to reduce water usage
              </li>
              <li className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Turn off the tap while brushing teeth or shaving
              </li>
            </>
          ) : (
            <>
              <li className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Turn off lights and appliances when not in use
              </li>
              <li className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Use energy-efficient LED bulbs
              </li>
              <li className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Set air conditioner to an optimal temperature (25-26°C)
              </li>
            </>
          )}
        </ul>
      </div>
    </div>
  );
}
