"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import RoomCard from '@/components/rooms/RoomCard';
import RoomFilter from '@/components/rooms/RoomFilter';
import RoomHeader from '@/components/rooms/RoomHeader';
import ThaiFooter from '@/components/layout/ThaiFooter';

// Define the room data structure
interface Room {
  id: string;
  title: string;
  price: string;
  numericPrice: number;
  description: string;
  imgSrc: string;
  features: string[];
  size: string;
  capacity: number;
  availability: string;
  type: string;
}

// Define the RoomType data structure from API
interface RoomTypeFromAPI {
  id: string;
  name: string;
  description: string;
  price: number;
  amenities: string[];
  imageUrl?: string;
  imageData?: string;
  fileType?: string;
  fileName?: string;
  roomCount: number;
  createdAt: string;
  updatedAt: string;
}

export default function RoomsPage() {
  const [roomsData, setRoomsData] = useState<Room[]>([]);
  const [filteredRooms, setFilteredRooms] = useState<Room[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [roomTypes, setRoomTypes] = useState<string[]>([]);
  const [maxPrice, setMaxPrice] = useState<number>(1000); // Default max price

  // Fetch room types from API
  useEffect(() => {
    const fetchRoomTypes = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/room-types');
        const data = await response.json();

        if (data.success && data.data.length > 0) {
          const apiRoomTypes: RoomTypeFromAPI[] = data.data;

          // Transform API data to match our Room interface
          const transformedRooms: Room[] = apiRoomTypes.map(roomType => {
            // Determine image source - use imageData if available, otherwise use imageUrl or fallback
            let imgSrc = '/images/noobroom.jpg'; // Fallback image
            if (roomType.imageData) {
              // Use the base64 image data directly
              imgSrc = roomType.imageData;
            } else if (roomType.imageUrl && roomType.imageUrl.trim() !== '') {
              imgSrc = roomType.imageUrl;
            }

            // Parse amenities if they exist
            let features: string[] = ['Fully furnished', 'High-speed internet'];
            if (roomType.amenities && Array.isArray(roomType.amenities)) {
              features = roomType.amenities;
            }

            // Determine capacity based on room type name (simple logic)
            const capacity = roomType.name.toLowerCase().includes('double') ? 2 : 1;

            // Determine availability based on room count
            const availability = roomType.roomCount > 5 ? 'Available' : 'Limited';

            // Determine room type category
            const type = roomType.name.split(' ')[0] || 'Standard'; // Use first word of name as type

            // Determine size (simple logic)
            const size = capacity === 1 ? '15 m²' : '22 m²';

            return {
              id: roomType.id,
              title: roomType.name,
              price: `฿${roomType.price}/month`,
              numericPrice: roomType.price,
              description: roomType.description || 'Comfortable room with essential amenities.',
              imgSrc,
              features,
              size,
              capacity,
              availability,
              type
            };
          });

          setRoomsData(transformedRooms);
          setFilteredRooms(transformedRooms);

          // Extract unique room types
          const types = Array.from(new Set(transformedRooms.map(room => room.type)));
          setRoomTypes(types);

          // Find max price
          const maxRoomPrice = Math.max(...transformedRooms.map(room => room.numericPrice));
          setMaxPrice(maxRoomPrice);
        }
      } catch (error) {
        console.error('Error fetching room types:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRoomTypes();
  }, []);

  // Handle filter changes
  const handleFilterChange = (filters: {
    type: string;
    capacity: string;
    priceRange: [number, number];
  }) => {
    const filtered = roomsData.filter(room => {
      // Filter by type
      const typeMatch = filters.type === 'all' || room.type === filters.type;

      // Filter by capacity
      let capacityMatch = true;
      if (filters.capacity !== 'all') {
        if (filters.capacity === '3') {
          capacityMatch = room.capacity >= 3;
        } else {
          capacityMatch = room.capacity === parseInt(filters.capacity);
        }
      }

      // Filter by price
      const priceMatch = room.numericPrice <= filters.priceRange[1];

      return typeMatch && capacityMatch && priceMatch;
    });

    setFilteredRooms(filtered);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <RoomHeader />

      <div className="container mx-auto px-4 pb-16" id="room-listings">
        <RoomFilter
          onFilterChange={handleFilterChange}
          roomTypes={roomTypes}
          maxPrice={maxPrice}
        />

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 h-full animate-pulse">
                <div className="h-64 bg-gray-200"></div>
                <div className="p-6">
                  <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-5/6 mb-6"></div>
                  <div className="h-10 bg-gray-200 rounded w-full"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredRooms.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-12 text-center border border-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <h3 className="text-xl font-bold text-gray-700 mb-2">No rooms found</h3>
            <p className="text-gray-500 mb-6">Try adjusting your filters to find available rooms</p>
            <button
              onClick={() => handleFilterChange({ type: 'all', capacity: 'all', priceRange: [0, maxPrice] })}
              className="px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition"
            >
              Reset Filters
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredRooms.map((room, index) => (
              <motion.div
                key={room.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <RoomCard
                  id={room.id}
                  title={room.title}
                  price={room.price}
                  description={room.description}
                  imgSrc={room.imgSrc}
                  features={room.features}
                  size={room.size}
                  capacity={room.capacity}
                  availability={room.availability}
                  type={room.type}
                />
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Newsletter Section */}

      {/* Footer */}
      <ThaiFooter />

    </div>
  );
}