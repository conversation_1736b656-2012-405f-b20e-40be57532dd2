"use client";

import { useState } from 'react';
import DashboardSidebar from './DashboardSidebar';

interface DashboardLayoutProps {
  children: React.ReactNode;
  activeItem: string;
}

export default function DashboardLayout({ children, activeItem }: DashboardLayoutProps) {
  const [activeTab, setActiveTab] = useState(activeItem);
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex flex-col md:flex-row">
        {/* Sidebar */}
        <DashboardSidebar activeTab={activeTab} setActiveTab={setActiveTab} />
        
        {/* Main Content */}
        <main className="flex-1 p-4 md:p-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-gray-800">
                  {activeTab === 'overview' && 'Dashboard Overview'}
                  {activeTab === 'payments' && 'Payment History'}
                  {activeTab === 'utilities' && 'Utility Usage'}
                  {activeTab === 'service-requests' && 'Service Requests'}
                  {activeTab === 'settings' && 'Account Settings'}
                </h1>
                <p className="text-gray-600">Welcome back, John Doe</p>
              </div>
              <div className="bg-white p-3 rounded-full shadow-sm">
                <span className="font-medium text-amber-600">Room A-101</span>
              </div>
            </div>
            
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
