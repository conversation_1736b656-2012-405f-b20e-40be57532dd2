"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Swal from 'sweetalert2';

interface ServiceResponseUser {
  id: string;
  name: string;
  role: string;
}

interface ServiceResponse {
  id: string;
  message: string;
  attachments?: string;
  createdAt: string;
  user?: ServiceResponseUser;
}

interface ServiceResponseListProps {
  serviceRequestId: string;
  onResponseAdded?: () => void;
}

export default function ServiceResponseList({ serviceRequestId, onResponseAdded }: ServiceResponseListProps) {
  const [responses, setResponses] = useState<ServiceResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newResponse, setNewResponse] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch responses
  const fetchResponses = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`/api/service-requests/${serviceRequestId}/responses`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch responses: ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch responses');
      }

      setResponses(data.data);
    } catch (err) {
      console.error('Error fetching responses:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while fetching responses');
    } finally {
      setIsLoading(false);
    }
  };

  // Submit a new response
  const handleSubmitResponse = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newResponse.trim()) {
      Swal.fire({
        title: 'Error',
        text: 'Please enter a response message',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`/api/service-requests/${serviceRequestId}/respond`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          message: newResponse,
          updateStatus: 'IN_PROGRESS' // Automatically set to in-progress when admin responds
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to submit response: ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to submit response');
      }

      // Clear the form
      setNewResponse('');

      // Refresh the responses
      fetchResponses();

      // Notify parent component
      if (onResponseAdded) {
        onResponseAdded();
      }

      Swal.fire({
        title: 'Response Sent',
        text: 'Your response has been sent successfully',
        icon: 'success',
        confirmButtonColor: '#f59e0b',
      });
    } catch (err) {
      console.error('Error submitting response:', err);
      Swal.fire({
        title: 'Error',
        text: err instanceof Error ? err.message : 'Failed to submit response',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Load responses on component mount
  useEffect(() => {
    fetchResponses();
  }, [serviceRequestId]);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">Responses</h3>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="font-medium">Error</p>
          <p className="text-sm">{error}</p>
          <button
            onClick={fetchResponses}
            className="mt-2 text-sm bg-red-100 hover:bg-red-200 text-red-800 py-1 px-3 rounded transition"
          >
            Try Again
          </button>
        </div>
      )}

      {/* Loading state */}
      {isLoading ? (
        <div className="space-y-4">
          {[...Array(2)].map((_, i) => (
            <div key={i} className="bg-gray-50 rounded-lg p-4 animate-pulse">
              <div className="flex justify-between mb-2">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/6"></div>
              </div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      ) : (
        <>
          {/* Response list */}
          {responses.length === 0 ? (
            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <p className="text-gray-500">No responses yet</p>
            </div>
          ) : (
            <div className="space-y-4">
              {responses.map((response) => (
                <motion.div
                  key={response.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`p-4 rounded-lg ${
                    response.user?.role === 'ADMIN'
                      ? 'bg-amber-50 border border-amber-100'
                      : 'bg-gray-50 border border-gray-100'
                  }`}
                >
                  <div className="flex justify-between mb-2">
                    <span className="font-medium">
                      {response.user?.name || 'Unknown User'}{' '}
                      <span className="text-xs px-2 py-1 rounded-full bg-gray-200 text-gray-700 ml-2">
                        {response.user?.role || 'Unknown Role'}
                      </span>
                    </span>
                    <span className="text-sm text-gray-500">{formatDate(response.createdAt)}</span>
                  </div>
                  <p className="text-gray-700 whitespace-pre-wrap">{response.message}</p>
                  {response.attachments && (
                    <div className="mt-2">
                      <a
                        href={response.attachments}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:underline flex items-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 mr-1"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M10.172 13.828a4 4 0 005.656 0l4-4a4 4 0 10-5.656-5.656l-1.102 1.101"
                          />
                        </svg>
                        Attachment
                      </a>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          )}

          {/* New response form */}
          <form onSubmit={handleSubmitResponse} className="mt-6">
            <div className="space-y-4">
              <div>
                <label htmlFor="newResponse" className="block text-sm font-medium text-gray-700 mb-1">
                  Add Response
                </label>
                <textarea
                  id="newResponse"
                  rows={4}
                  className="w-full px-3 py-2 text-gray-700 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                  placeholder="Enter your response..."
                  value={newResponse}
                  onChange={(e) => setNewResponse(e.target.value)}
                  disabled={isSubmitting}
                ></textarea>
              </div>
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition ${
                    isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  {isSubmitting ? (
                    <>
                      <span className="inline-block animate-spin mr-2">⟳</span>
                      Sending...
                    </>
                  ) : (
                    'Send Response'
                  )}
                </button>
              </div>
            </div>
          </form>
        </>
      )}
    </div>
  );
}
