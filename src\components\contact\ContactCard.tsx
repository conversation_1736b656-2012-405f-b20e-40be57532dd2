"use client";

import { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface ContactCardProps {
  icon: ReactNode;
  title: string;
  content: ReactNode;
  delay?: number;
}

export default function ContactCard({ icon, title, content, delay = 0 }: ContactCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}
      className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 h-full"
    >
      <div className="flex items-start">
        <div className="bg-amber-100 rounded-full p-3 mr-4 text-amber-600">
          {icon}
        </div>
        <div>
          <h3 className="text-lg font-semibold mb-2 text-gray-900">{title}</h3>
          <div className="text-gray-600">{content}</div>
        </div>
      </div>
    </motion.div>
  );
}
