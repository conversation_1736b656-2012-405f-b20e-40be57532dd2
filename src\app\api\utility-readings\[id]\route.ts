import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET a specific utility reading by ID
async function handleGet(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const utilityReading = await prisma.utilityReading.findUnique({
      where: { id },
      include: {
        room: {
          select: {
            roomNumber: true,
            roomType: {
              select: {
                name: true
              }
            },
            residents: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    });

    if (!utilityReading) {
      return NextResponse.json(
        { success: false, message: 'Utility reading not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: utilityReading
    });
  } catch (error) {
    console.error(`Error fetching utility reading with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch utility reading' },
      { status: 500 }
    );
  }
}

// PUT update a utility reading
async function handlePut(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { 
      waterReading, 
      electricityReading, 
      readingDate,
      waterUsage,
      electricityUsage,
      waterCost,
      electricityCost,
      totalCost,
      isPaid
    } = body;

    // Check if utility reading exists
    const existingReading = await prisma.utilityReading.findUnique({
      where: { id }
    });

    if (!existingReading) {
      return NextResponse.json(
        { success: false, message: 'Utility reading not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};
    
    if (waterReading !== undefined) updateData.waterReading = waterReading;
    if (electricityReading !== undefined) updateData.electricityReading = electricityReading;
    if (readingDate) updateData.readingDate = new Date(readingDate);
    if (waterUsage !== undefined) updateData.waterUsage = waterUsage;
    if (electricityUsage !== undefined) updateData.electricityUsage = electricityUsage;
    if (waterCost !== undefined) updateData.waterCost = waterCost;
    if (electricityCost !== undefined) updateData.electricityCost = electricityCost;
    if (totalCost !== undefined) updateData.totalCost = totalCost;
    if (isPaid !== undefined) updateData.isPaid = isPaid;

    // If water or electricity readings are updated, recalculate usage and costs
    if ((waterReading !== undefined || electricityReading !== undefined) && 
        (waterUsage === undefined || electricityUsage === undefined || 
         waterCost === undefined || electricityCost === undefined || 
         totalCost === undefined)) {
      
      // Get the previous reading for this room to calculate usage
      const previousReading = await prisma.utilityReading.findFirst({
        where: { 
          roomId: existingReading.roomId,
          readingDate: {
            lt: existingReading.readingDate
          }
        },
        orderBy: { readingDate: 'desc' }
      });

      if (previousReading) {
        // Thailand utility rates (example values)
        const waterRate = 18; // ฿18 per cubic meter
        const electricityRate = 4; // ฿4 per kWh
        
        // Use new readings if provided, otherwise use existing readings
        const currentWaterReading = waterReading !== undefined ? waterReading : existingReading.waterReading;
        const currentElectricityReading = electricityReading !== undefined ? electricityReading : existingReading.electricityReading;
        
        // Calculate usage
        if (waterUsage === undefined) {
          updateData.waterUsage = Math.max(0, currentWaterReading - previousReading.waterReading);
        }
        
        if (electricityUsage === undefined) {
          updateData.electricityUsage = Math.max(0, currentElectricityReading - previousReading.electricityReading);
        }
        
        // Calculate costs
        if (waterCost === undefined) {
          updateData.waterCost = updateData.waterUsage * waterRate;
        }
        
        if (electricityCost === undefined) {
          updateData.electricityCost = updateData.electricityUsage * electricityRate;
        }
        
        if (totalCost === undefined) {
          updateData.totalCost = updateData.waterCost + updateData.electricityCost;
        }
      }
    }

    // Update the utility reading
    const updatedReading = await prisma.utilityReading.update({
      where: { id },
      data: updateData,
      include: {
        room: {
          select: {
            roomNumber: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Utility reading updated successfully',
      data: updatedReading
    });
  } catch (error) {
    console.error(`Error updating utility reading with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to update utility reading' },
      { status: 500 }
    );
  }
}

// DELETE a utility reading
async function handleDelete(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Check if utility reading exists
    const existingReading = await prisma.utilityReading.findUnique({
      where: { id }
    });

    if (!existingReading) {
      return NextResponse.json(
        { success: false, message: 'Utility reading not found' },
        { status: 404 }
      );
    }

    // Check if the reading is already paid
    if (existingReading.isPaid) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Cannot delete a utility reading that has been paid. Mark it as unpaid first.' 
        },
        { status: 400 }
      );
    }

    // Delete the utility reading
    await prisma.utilityReading.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Utility reading deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting utility reading with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete utility reading' },
      { status: 500 }
    );
  }
}

// Export the handlers with authentication middleware
export const GET = withAuth(handleGet, ['ADMIN']);
export const PUT = withAuth(handlePut, ['ADMIN']);
export const DELETE = withAuth(handleDelete, ['ADMIN']);
