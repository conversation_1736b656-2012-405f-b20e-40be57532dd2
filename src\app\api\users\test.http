### Get all users
GET http://localhost:3000/api/users
Authorization: Bearer YOUR_TOKEN_HERE

### Get all users with filtering
GET http://localhost:3000/api/users?role=RESIDENT&search=john&page=1&limit=10
Authorization: Bearer YOUR_TOKEN_HERE

### Get a specific user
GET http://localhost:3000/api/users/USER_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

### Create a new user
POST http://localhost:3000/api/users
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "************",
  "address": "123 Main St",
  "role": "VISITORS"
}

### Update a user
PUT http://localhost:3000/api/users/USER_ID_HERE
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "name": "<PERSON> Updated",
  "phone": "************",
  "address": "124 Main St"
}

### Delete a user
DELETE http://localhost:3000/api/users/USER_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

### Assign a room to a user
POST http://localhost:3000/api/users/USER_ID_HERE/assign-room
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "roomId": "ROOM_ID_HERE"
}

### Remove a room assignment from a user
DELETE http://localhost:3000/api/users/USER_ID_HERE/assign-room?roomId=ROOM_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

### Change a user's role
POST http://localhost:3000/api/users/USER_ID_HERE/change-role
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "role": "ADMIN"
}
