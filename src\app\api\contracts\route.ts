import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET all contracts (with optional filtering)
async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const roomId = searchParams.get('roomId');
    const isActive = searchParams.get('isActive');

    // Build the query
    const query: any = {
      where: {},
      include: {
        room: {
          select: {
            id: true,
            roomNumber: true,
            roomType: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    };

    // Add filters if provided
    if (userId) {
      query.where.residentId = userId;
    }

    if (roomId) {
      query.where.roomId = roomId;
    }

    if (isActive !== null) {
      query.where.isActive = isActive === 'true';
    }

    const contracts = await prisma.contract.findMany(query);

    return NextResponse.json({
      success: true,
      data: contracts
    });
  } catch (error) {
    console.error('Error fetching contracts:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch contracts' },
      { status: 500 }
    );
  }
}

// POST create a new contract
async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { roomId, startDate, endDate, rentAmount, depositAmount } = body;

    // Get the authenticated user from the request
    const user = (request as any).user;

    // Validate required fields
    if (!roomId || !startDate || !endDate || !rentAmount) {
      return NextResponse.json(
        { success: false, message: 'Required fields are missing' },
        { status: 400 }
      );
    }

    // Check if room exists and is not occupied
    const room = await prisma.room.findUnique({
      where: { id: roomId }
    });

    if (!room) {
      return NextResponse.json(
        { success: false, message: 'Room not found' },
        { status: 404 }
      );
    }

    if (room.isOccupied) {
      return NextResponse.json(
        { success: false, message: 'Room is already occupied' },
        { status: 400 }
      );
    }

    // Create the contract
    const contract = await prisma.contract.create({
      data: {
        residentId: user.id,
        roomId,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        rentAmount,
        depositAmount: depositAmount || rentAmount * 2, // Default to 2 months rent if not specified
        isActive: false // Will be set to true after payment
      }
    });

    return NextResponse.json(
      {
        success: true,
        message: 'Contract created successfully',
        data: contract
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating contract:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create contract' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware to the handlers
const protectedGET = withAuth(GET, ['ADMIN', 'RESIDENT']);
const protectedPOST = withAuth(POST, ['ADMIN', 'VISITORS', 'RESIDENT']);

export { protectedGET as GET, protectedPOST as POST };
