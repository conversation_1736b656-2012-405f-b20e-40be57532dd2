import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET all utility readings with optional filtering
async function handleGet(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const roomId = url.searchParams.get('roomId');
    const isPaid = url.searchParams.get('isPaid');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;
    
    // Build the where clause based on query parameters
    const where: any = {};
    
    if (roomId) {
      where.roomId = roomId;
    }
    
    if (isPaid !== null) {
      where.isPaid = isPaid === 'true';
    }
    
    // Add date range filter if provided
    if (startDate || endDate) {
      where.readingDate = {};
      
      if (startDate) {
        where.readingDate.gte = new Date(startDate);
      }
      
      if (endDate) {
        where.readingDate.lte = new Date(endDate);
      }
    }
    
    // Get total count for pagination
    const totalCount = await prisma.utilityReading.count({ where });
    
    // Get utility readings with pagination
    const utilityReadings = await prisma.utilityReading.findMany({
      where,
      include: {
        room: {
          select: {
            roomNumber: true,
            roomType: {
              select: {
                name: true
              }
            },
            residents: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      },
      orderBy: {
        readingDate: 'desc'
      },
      skip,
      take: limit
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    
    return NextResponse.json({
      success: true,
      data: utilityReadings,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching utility readings:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch utility readings' },
      { status: 500 }
    );
  }
}

// POST create a new utility reading
async function handlePost(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      roomId, 
      waterReading, 
      electricityReading, 
      readingDate = new Date(),
      waterUsage,
      electricityUsage,
      waterCost,
      electricityCost,
      totalCost,
      isPaid = false
    } = body;

    // Validate required fields
    if (!roomId || waterReading === undefined || electricityReading === undefined) {
      return NextResponse.json(
        { success: false, message: 'Room ID, water reading, and electricity reading are required' },
        { status: 400 }
      );
    }

    // Check if room exists
    const room = await prisma.room.findUnique({
      where: { id: roomId }
    });

    if (!room) {
      return NextResponse.json(
        { success: false, message: 'Room not found' },
        { status: 404 }
      );
    }

    // Get the previous reading for this room to calculate usage
    const previousReading = await prisma.utilityReading.findFirst({
      where: { roomId },
      orderBy: { readingDate: 'desc' }
    });

    // Calculate usage and costs if not provided
    let calculatedWaterUsage = waterUsage;
    let calculatedElectricityUsage = electricityUsage;
    let calculatedWaterCost = waterCost;
    let calculatedElectricityCost = electricityCost;
    let calculatedTotalCost = totalCost;

    if (previousReading) {
      // Calculate usage if not provided
      if (calculatedWaterUsage === undefined) {
        calculatedWaterUsage = Math.max(0, waterReading - previousReading.waterReading);
      }
      
      if (calculatedElectricityUsage === undefined) {
        calculatedElectricityUsage = Math.max(0, electricityReading - previousReading.electricityReading);
      }
      
      // Thailand utility rates (example values)
      const waterRate = 18; // ฿18 per cubic meter
      const electricityRate = 4; // ฿4 per kWh
      
      // Calculate costs if not provided
      if (calculatedWaterCost === undefined) {
        calculatedWaterCost = calculatedWaterUsage * waterRate;
      }
      
      if (calculatedElectricityCost === undefined) {
        calculatedElectricityCost = calculatedElectricityUsage * electricityRate;
      }
      
      if (calculatedTotalCost === undefined) {
        calculatedTotalCost = calculatedWaterCost + calculatedElectricityCost;
      }
    } else {
      // If this is the first reading, set usage and costs to 0 if not provided
      calculatedWaterUsage = calculatedWaterUsage || 0;
      calculatedElectricityUsage = calculatedElectricityUsage || 0;
      calculatedWaterCost = calculatedWaterCost || 0;
      calculatedElectricityCost = calculatedElectricityCost || 0;
      calculatedTotalCost = calculatedTotalCost || 0;
    }

    // Create the utility reading
    const utilityReading = await prisma.utilityReading.create({
      data: {
        roomId,
        waterReading,
        electricityReading,
        readingDate: new Date(readingDate),
        waterUsage: calculatedWaterUsage,
        electricityUsage: calculatedElectricityUsage,
        waterCost: calculatedWaterCost,
        electricityCost: calculatedElectricityCost,
        totalCost: calculatedTotalCost,
        isPaid
      },
      include: {
        room: {
          select: {
            roomNumber: true
          }
        }
      }
    });

    return NextResponse.json(
      { 
        success: true, 
        message: 'Utility reading created successfully',
        data: utilityReading
      },
      { status: 201 }
    );
    
  } catch (error) {
    console.error('Error creating utility reading:', error);
    return NextResponse.json(
      { success: false, message: 'An error occurred while creating the utility reading' },
      { status: 500 }
    );
  }
}

// Export the handlers with authentication middleware
export const GET = withAuth(handleGet, ['ADMIN']);
export const POST = withAuth(handlePost, ['ADMIN']);
