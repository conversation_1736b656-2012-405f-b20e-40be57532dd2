"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';
import <PERSON><PERSON>ield from '@/components/admin/FormField';
import Swal from 'sweetalert2';

interface MeterReadingFormProps {
  roomId: string;
  roomNumber: string;
  currentWaterReading?: number;
  currentElectricityReading?: number;
  previousWaterReading?: number;
  previousElectricityReading?: number;
  isCurrentMonthRecorded?: boolean;
  lastReadingMonth?: number;
  lastReadingYear?: number;
  onSubmit: (data: {
    roomId: string;
    waterReading: number;
    electricityReading: number;
  }) => void;
  isSubmitting?: boolean;
}

export default function MeterReadingForm({
  roomId,
  roomNumber,
  currentWaterReading = 0,
  currentElectricityReading = 0,
  previousWaterReading = 0,
  previousElectricityReading = 0,
  isCurrentMonthRecorded = false,
  lastReadingMonth,
  lastReadingYear,
  onSubmit,
  isSubmitting = false
}: MeterReadingFormProps) {
  // Get current month name for display
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  const currentMonthName = new Intl.DateTimeFormat('en-US', { month: 'long' }).format(currentDate);

  // Get last reading month name if available
  let lastReadingMonthName = '';
  if (lastReadingMonth !== undefined && lastReadingYear !== undefined) {
    const lastReadingDate = new Date(lastReadingYear, lastReadingMonth);
    lastReadingMonthName = new Intl.DateTimeFormat('en-US', { month: 'long', year: 'numeric' }).format(lastReadingDate);
  }
  const [waterReading, setWaterReading] = useState(currentWaterReading);
  const [electricityReading, setElectricityReading] = useState(currentElectricityReading);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Calculate usage
  const waterUsage = Math.max(0, waterReading - previousWaterReading);
  const electricityUsage = Math.max(0, electricityReading - previousElectricityReading);

  // Thailand utility rates (example values)
  const waterRate = 18; // ฿18 per cubic meter
  const electricityRate = 4; // ฿4 per kWh

  // Calculate costs
  const waterCost = waterUsage * waterRate;
  const electricityCost = electricityUsage * electricityRate;
  const totalCost = waterCost + electricityCost;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const newErrors: Record<string, string> = {};

    if (waterReading < previousWaterReading) {
      newErrors.waterReading = 'New reading cannot be less than previous reading';
    }

    if (electricityReading < previousElectricityReading) {
      newErrors.electricityReading = 'New reading cannot be less than previous reading';
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      // Calculate usage for display in the confirmation
      const waterUsageDisplay = Math.max(0, waterReading - previousWaterReading);
      const electricityUsageDisplay = Math.max(0, electricityReading - previousElectricityReading);
      const waterCostDisplay = waterUsageDisplay * waterRate;
      const electricityCostDisplay = electricityUsageDisplay * electricityRate;
      const totalCostDisplay = waterCostDisplay + electricityCostDisplay;

      // Show SweetAlert2 confirmation
      Swal.fire({
        title: 'Confirm Meter Readings',
        html: `
          <div class="text-left">
            <p class="mb-4">Are you sure you want to save the following readings for Room ${roomNumber}?</p>

            <div class="mb-3">
              <h4 class="font-bold text-sm">Water Meter</h4>
              <div class="grid grid-cols-2 text-sm">
                <span>Current Reading:</span>
                <span class="text-right">${waterReading} m³</span>
                <span>Usage:</span>
                <span class="text-right">${waterUsageDisplay} m³</span>
                <span>Cost:</span>
                <span class="text-right">฿${waterCostDisplay.toFixed(2)}</span>
              </div>
            </div>

            <div class="mb-3">
              <h4 class="font-bold text-sm">Electricity Meter</h4>
              <div class="grid grid-cols-2 text-sm">
                <span>Current Reading:</span>
                <span class="text-right">${electricityReading} kWh</span>
                <span>Usage:</span>
                <span class="text-right">${electricityUsageDisplay} kWh</span>
                <span>Cost:</span>
                <span class="text-right">฿${electricityCostDisplay.toFixed(2)}</span>
              </div>
            </div>

            <div class="border-t pt-2 mt-2">
              <div class="grid grid-cols-2 text-sm font-bold">
                <span>Total Cost:</span>
                <span class="text-right text-amber-600">฿${totalCostDisplay.toFixed(2)}</span>
              </div>
            </div>
          </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#f59e0b',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Save Readings',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        focusConfirm: false,
      }).then((result) => {
        if (result.isConfirmed) {
          // Submit the form
          onSubmit({
            roomId,
            waterReading,
            electricityReading
          });

          // Show success message
          Swal.fire({
            title: 'Readings Saved!',
            text: `Meter readings for Room ${roomNumber} have been recorded successfully.`,
            icon: 'success',
            confirmButtonColor: '#f59e0b',
          });
        }
      });
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div className="flex justify-between items-center mb-4">
        <div>
          <div className="flex items-center">
            <h3 className="text-lg font-medium">Room {roomNumber}</h3>
            {isCurrentMonthRecorded ? (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <svg className="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Recorded for {currentMonthName}
              </span>
            ) : (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                <svg className="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                Pending for {currentMonthName}
              </span>
            )}
          </div>
          {lastReadingMonthName && !isCurrentMonthRecorded && (
            <div className="mt-1 text-xs text-gray-500">
              Last recorded: {lastReadingMonthName}
            </div>
          )}
        </div>
        <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm font-medium">
          Total: ฿{totalCost.toFixed(2)}
        </span>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-4">Water Meter</h4>
            <div className="space-y-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Previous Reading:</span>
                <span className="font-medium">{previousWaterReading} m³</span>
              </div>

              <FormField
                label="Current Reading (m³)"
                name="waterReading"
                type="number"
                value={waterReading}
                onChange={(e) => setWaterReading(Number(e.target.value))}
                error={errors.waterReading}
                required
              />

              <div className="flex justify-between text-sm pt-2 border-t">
                <span className="text-gray-500">Usage:</span>
                <span className="font-medium">{waterUsage} m³</span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Rate:</span>
                <span className="font-medium">฿{waterRate}/m³</span>
              </div>

              <div className="flex justify-between text-sm font-medium">
                <span>Subtotal:</span>
                <span className="text-amber-600">฿{waterCost.toFixed(2)}</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-4">Electricity Meter</h4>
            <div className="space-y-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Previous Reading:</span>
                <span className="font-medium">{previousElectricityReading} kWh</span>
              </div>

              <FormField
                label="Current Reading (kWh)"
                name="electricityReading"
                type="number"
                value={electricityReading}
                onChange={(e) => setElectricityReading(Number(e.target.value))}
                error={errors.electricityReading}
                required
              />

              <div className="flex justify-between text-sm pt-2 border-t">
                <span className="text-gray-500">Usage:</span>
                <span className="font-medium">{electricityUsage} kWh</span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Rate:</span>
                <span className="font-medium">฿{electricityRate}/kWh</span>
              </div>

              <div className="flex justify-between text-sm font-medium">
                <span>Subtotal:</span>
                <span className="text-amber-600">฿{electricityCost.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm">
            <span className="font-medium">Total Cost: </span>
            <span className="text-lg font-bold text-amber-600">฿{totalCost.toFixed(2)}</span>
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition ${
              isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {isSubmitting ? (
              <>
                <span className="inline-block animate-spin mr-2">⟳</span>
                Saving...
              </>
            ) : (
              'Save Readings'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
