import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

// GET a specific payment by ID
async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const { id } = params;

    // Get the payment
    const payment = await prisma.payment.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        room: {
          select: {
            id: true,
            roomNumber: true,
            roomType: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    if (!payment) {
      return NextResponse.json(
        { success: false, message: 'Payment not found' },
        { status: 404 }
      );
    }

    // Format the response
    const formattedPayment = {
      id: payment.id,
      amount: payment.amount,
      dueDate: payment.dueDate,
      paidDate: payment.paidDate,
      status: payment.status,
      type: payment.type,
      notes: payment.notes,
      receiptNumber: payment.receiptNumber,
      receiptUrl: payment.receiptUrl,
      paymentMethod: payment.paymentMethod,
      user: {
        id: payment.user.id,
        name: payment.user.name,
        email: payment.user.email,
      },
      room: {
        id: payment.room.id,
        roomNumber: payment.room.roomNumber,
        roomType: payment.room.roomType.name,
      },
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
    };

    return NextResponse.json({
      success: true,
      data: formattedPayment,
    });
  } catch (error) {
    console.error('Error fetching payment:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch payment' },
      { status: 500 }
    );
  }
}

// PUT to update a payment
async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const { 
      amount, 
      dueDate, 
      paidDate, 
      status, 
      notes,
      paymentMethod,
      receiptNumber 
    } = body;

    // Get the current payment to check if status is changing
    const currentPayment = await prisma.payment.findUnique({
      where: { id },
      select: { status: true, userId: true, amount: true, type: true }
    });

    if (!currentPayment) {
      return NextResponse.json(
        { success: false, message: 'Payment not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};
    
    if (amount !== undefined) updateData.amount = parseFloat(amount);
    if (dueDate !== undefined) updateData.dueDate = new Date(dueDate);
    if (status !== undefined) updateData.status = status.toUpperCase();
    if (notes !== undefined) updateData.notes = notes;
    if (paymentMethod !== undefined) updateData.paymentMethod = paymentMethod;
    if (receiptNumber !== undefined) updateData.receiptNumber = receiptNumber;
    
    // Handle paid date based on status
    if (status === 'PAID' && !paidDate) {
      // If marking as paid and no paid date provided, use current date
      updateData.paidDate = new Date();
    } else if (paidDate) {
      updateData.paidDate = new Date(paidDate);
    } else if (status === 'PENDING' || status === 'UPCOMING') {
      // If marking as pending or upcoming, clear paid date
      updateData.paidDate = null;
    }

    // Update the payment
    const updatedPayment = await prisma.payment.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        room: {
          select: {
            id: true,
            roomNumber: true,
            roomType: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    // Create a notification if status changed to PAID
    if (currentPayment.status !== 'PAID' && status === 'PAID') {
      await prisma.notification.create({
        data: {
          userId: currentPayment.userId,
          title: 'Payment Received',
          message: `Your ${currentPayment.type.toLowerCase()} payment of ฿${currentPayment.amount} has been marked as paid.`,
          type: 'PAYMENT_RECEIVED',
          isRead: false,
          link: '/user/dashboard/payments',
        },
      });
    }

    // Format the response
    const formattedPayment = {
      id: updatedPayment.id,
      amount: updatedPayment.amount,
      dueDate: updatedPayment.dueDate,
      paidDate: updatedPayment.paidDate,
      status: updatedPayment.status,
      type: updatedPayment.type,
      notes: updatedPayment.notes,
      receiptNumber: updatedPayment.receiptNumber,
      receiptUrl: updatedPayment.receiptUrl,
      paymentMethod: updatedPayment.paymentMethod,
      user: {
        id: updatedPayment.user.id,
        name: updatedPayment.user.name,
        email: updatedPayment.user.email,
      },
      room: {
        id: updatedPayment.room.id,
        roomNumber: updatedPayment.room.roomNumber,
        roomType: updatedPayment.room.roomType.name,
      },
      createdAt: updatedPayment.createdAt,
      updatedAt: updatedPayment.updatedAt,
    };

    return NextResponse.json({
      success: true,
      data: formattedPayment,
    });
  } catch (error) {
    console.error('Error updating payment:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update payment' },
      { status: 500 }
    );
  }
}

// DELETE a payment
async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const { id } = params;

    // Check if payment exists
    const payment = await prisma.payment.findUnique({
      where: { id },
    });

    if (!payment) {
      return NextResponse.json(
        { success: false, message: 'Payment not found' },
        { status: 404 }
      );
    }

    // Delete the payment
    await prisma.payment.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: 'Payment deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting payment:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete payment' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedGET = withAuth(GET, ['ADMIN']);
const protectedPUT = withAuth(PUT, ['ADMIN']);
const protectedDELETE = withAuth(DELETE, ['ADMIN']);

export { protectedGET as GET, protectedPUT as PUT, protectedDELETE as DELETE };
