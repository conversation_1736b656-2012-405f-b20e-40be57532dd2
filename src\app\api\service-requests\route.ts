import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET all service requests with optional filtering
async function handleGet(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const priority = url.searchParams.get('priority');
    const userId = url.searchParams.get('userId');
    const roomId = url.searchParams.get('roomId');
    const type = url.searchParams.get('type');
    const search = url.searchParams.get('search');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build the where clause based on query parameters
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (priority) {
      where.priority = priority;
    }

    if (userId) {
      where.userId = userId;
    }

    if (roomId) {
      where.roomId = roomId;
    }

    if (type) {
      where.type = type;
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Get total count for pagination
    const totalCount = await prisma.serviceRequest.count({ where });

    // Get service requests with pagination
    const serviceRequests = await prisma.serviceRequest.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        room: {
          select: {
            id: true,
            roomNumber: true,
            roomType: {
              select: {
                name: true
              }
            }
          }
        },
        responses: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 1 // Get only the latest response
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      success: true,
      data: serviceRequests,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching service requests:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch service requests' },
      { status: 500 }
    );
  }
}

// POST create a new service request (for residents)
async function handlePost(request: NextRequest) {
  try {
    console.log('POST /api/service-requests - Request received');

    // Get the user from the request object (added by withAuth middleware)
    const user = (request as any).user;
    console.log('User from request:', user ? { id: user.id, role: user.role } : 'No user');

    if (!user || !user.id) {
      console.log('User not authenticated - No user object found');
      return NextResponse.json(
        { success: false, message: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Check if user has the RESIDENT role
    if (user.role !== 'RESIDENT') {
      console.log(`User role check failed - Current role: ${user.role}`);
      return NextResponse.json(
        { success: false, message: 'Only residents can create service requests' },
        { status: 403 }
      );
    }

    const body = await request.json();
    console.log('Request body:', body);

    const {
      title,
      description,
      type,
      priority = 'MEDIUM',
      roomId,
      images = []
    } = body;

    // Use the user ID from the decoded token
    const userId = user.id;

    // Validate required fields
    if (!title || !description || !type || !roomId) {
      return NextResponse.json(
        { success: false, message: 'Title, description, type, and roomId are required' },
        { status: 400 }
      );
    }

    // First check if the room exists
    const roomExists = await prisma.room.findUnique({
      where: { id: roomId }
    });

    if (!roomExists) {
      console.log(`Room with ID ${roomId} not found`);
      return NextResponse.json(
        { success: false, message: 'Room not found' },
        { status: 404 }
      );
    }

    // Then check if the room belongs to the user
    const room = await prisma.room.findFirst({
      where: {
        id: roomId,
        residents: {
          some: {
            id: userId
          }
        }
      }
    });

    if (!room) {
      console.log(`Room ${roomId} does not belong to user ${userId}`);
      return NextResponse.json(
        { success: false, message: 'Room does not belong to the user' },
        { status: 403 }
      );
    }

    console.log(`Room ${roomId} belongs to user ${userId}, proceeding with service request creation`);

    // Create the service request
    const serviceRequest = await prisma.serviceRequest.create({
      data: {
        title,
        description,
        type: type as any,
        priority: priority as any,
        userId,
        roomId,
        status: 'PENDING',
        images: {
          create: images.map((url: string) => ({
            url
          }))
        }
      },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        },
        room: {
          select: {
            roomNumber: true
          }
        },
        images: true
      }
    });

    // Create a notification for admins
    await prisma.notification.create({
      data: {
        userId, // Send to the user who created the request
        title: 'Service Request Submitted',
        message: `Your service request "${title}" has been submitted successfully.`,
        type: 'SERVICE_REQUEST_UPDATE'
      }
    });

    // Find admin users to notify them
    const admins = await prisma.user.findMany({
      where: {
        role: 'ADMIN'
      }
    });

    // Create notifications for all admins
    for (const admin of admins) {
      await prisma.notification.create({
        data: {
          userId: admin.id,
          title: 'New Service Request',
          message: `A new service request has been submitted by ${serviceRequest.user.name} from room ${serviceRequest.room.roomNumber}.`,
          type: 'SERVICE_REQUEST_UPDATE',
          link: `/admin/service-requests/${serviceRequest.id}`
        }
      });
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Service request created successfully',
        data: serviceRequest
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Error creating service request:', error);

    // Provide more detailed error message based on the error type
    let errorMessage = 'An error occurred while creating the service request';
    let statusCode = 500;

    if (error instanceof Error) {
      console.error('Error details:', error.message);
      errorMessage = `Error: ${error.message}`;

      // Check for specific error types
      if (error.message.includes('foreign key constraint')) {
        errorMessage = 'Invalid room ID or user ID';
        statusCode = 400;
      } else if (error.message.includes('unique constraint')) {
        errorMessage = 'A service request with these details already exists';
        statusCode = 409;
      }
    }

    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: statusCode }
    );
  }
}

// Export the handlers with authentication middleware
// GET - Admin can see all, residents can only see their own (handled in the function)
export const GET = withAuth(handleGet, ['ADMIN', 'RESIDENT']);
// POST - Allow any authenticated user to make a request, but we'll check the role in the handler
export const POST = withAuth(handlePost);
