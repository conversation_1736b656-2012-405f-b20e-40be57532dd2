import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import Strip<PERSON> from 'stripe';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_your_test_key');

// This is your Stripe webhook secret for testing your endpoint locally
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature') || '';

    let event;

    try {
      if (!endpointSecret) {
        throw new Error('Webhook secret is not configured');
      }
      event = stripe.webhooks.constructEvent(body, signature, endpointSecret);
    } catch (err: any) {
      console.error(`Webhook signature verification failed: ${err.message}`);
      return NextResponse.json(
        { success: false, message: `Webhook Error: ${err.message}` },
        { status: 400 }
      );
    }

    // Handle the event
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object as Stripe.Checkout.Session;

      // Get metadata from the session
      const { contractId, paymentId, userId, roomId, isContractPayment } = session.metadata || {};

      if (!userId || !roomId) {
        console.error('Missing required metadata in Stripe session');
        return NextResponse.json(
          { success: false, message: 'Missing required metadata in Stripe session' },
          { status: 400 }
        );
      }

      // Check if this is a contract payment or individual payment
      if (isContractPayment === 'true' && contractId) {
        // Contract payment flow
        // Update the contract to active
        await prisma.contract.update({
          where: { id: contractId },
          data: { isActive: true }
        });

        // Update the user role to RESIDENT
        await prisma.user.update({
          where: { id: userId },
          data: { role: 'RESIDENT' }
        });

        // Update the room to occupied and assign the user
        await prisma.room.update({
          where: { id: roomId },
          data: {
            isOccupied: true,
            residents: {
              connect: { id: userId }
            }
          }
        });

        // Create payment records
        // First month rent
        await prisma.payment.create({
          data: {
            amount: parseFloat(((session.amount_total || 0) / 100).toFixed(2)), // Convert from cents
            dueDate: new Date(),
            paidDate: new Date(),
            status: 'PAID',
            type: 'RENT',
            notes: 'First month rent payment',
            receiptNumber: `RCT-${new Date().getFullYear()}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
            paymentMethod: 'Credit Card (Stripe)',
            userId,
            roomId
          }
        });

        // Security deposit
        await prisma.payment.create({
          data: {
            amount: parseFloat(((session.amount_total || 0) / 200).toFixed(2)), // Assuming deposit is half of total
            dueDate: new Date(),
            paidDate: new Date(),
            status: 'PAID',
            type: 'DEPOSIT',
            notes: 'Security deposit payment',
            receiptNumber: `RCT-${new Date().getFullYear()}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
            paymentMethod: 'Credit Card (Stripe)',
            userId,
            roomId
          }
        });
      } else if (isContractPayment === 'false' && paymentId) {
        // Individual payment flow
        // Update the payment status to PAID
        await prisma.payment.update({
          where: { id: paymentId },
          data: {
            status: 'PAID',
            paidDate: new Date(),
            paymentMethod: 'Credit Card (Stripe)',
            notes: 'Payment processed via Stripe',
            receiptNumber: `RCT-${new Date().getFullYear()}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`
          }
        });
      } else {
        console.error('Invalid payment type or missing ID in metadata');
        return NextResponse.json(
          { success: false, message: 'Invalid payment type or missing ID in metadata' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json({ success: true, received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { success: false, message: 'Webhook error' },
      { status: 500 }
    );
  }
}

// Disable body parsing, we need the raw body for Stripe signature verification
export const config = {
  api: {
    bodyParser: false,
  },
};
