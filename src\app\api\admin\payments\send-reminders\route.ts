import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

// POST to send payment reminders
async function POST(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();
    const { paymentIds } = body;

    // Validate required fields
    if (!paymentIds || !Array.isArray(paymentIds) || paymentIds.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Payment IDs are required' },
        { status: 400 }
      );
    }

    // Get the payments
    const payments = await prisma.payment.findMany({
      where: {
        id: { in: paymentIds },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        room: {
          select: {
            roomNumber: true,
          },
        },
      },
    });

    if (payments.length === 0) {
      return NextResponse.json(
        { success: false, message: 'No payments found with the provided IDs' },
        { status: 404 }
      );
    }

    // Create notifications for each payment
    const notifications = [];
    for (const payment of payments) {
      const dueDate = new Date(payment.dueDate).toLocaleDateString();
      const isPastDue = new Date(payment.dueDate) < new Date();
      
      const notification = await prisma.notification.create({
        data: {
          userId: payment.userId,
          title: isPastDue ? 'Payment Overdue' : 'Payment Reminder',
          message: isPastDue
            ? `Your ${payment.type.toLowerCase()} payment of ฿${payment.amount} for room ${payment.room.roomNumber} was due on ${dueDate}. Please make your payment as soon as possible.`
            : `Your ${payment.type.toLowerCase()} payment of ฿${payment.amount} for room ${payment.room.roomNumber} is due on ${dueDate}. Please make your payment on time.`,
          type: 'PAYMENT_DUE',
          isRead: false,
          link: '/user/dashboard/payments',
        },
      });
      
      notifications.push(notification);
      
      // If the payment is past due, update its status to OVERDUE
      if (isPastDue && payment.status === 'PENDING') {
        await prisma.payment.update({
          where: { id: payment.id },
          data: { status: 'OVERDUE' },
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: `Sent ${notifications.length} payment reminders`,
      data: {
        notifications,
      },
    });
  } catch (error) {
    console.error('Error sending payment reminders:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to send payment reminders' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedPOST = withAuth(POST, ['ADMIN']);

export { protectedPOST as POST };
