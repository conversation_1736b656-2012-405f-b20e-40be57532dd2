"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';

interface ServiceRequest {
  id: string;
  type: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'emergency';
  createdAt: string;
  updatedAt: string;
  adminResponse?: string;
  scheduledDate?: string;
}

interface ServiceRequestHistoryProps {
  requests: ServiceRequest[];
}

export default function ServiceRequestHistory({ requests }: ServiceRequestHistoryProps) {
  const [expandedRequestId, setExpandedRequestId] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState('all');

  const filteredRequests = requests.filter(request => 
    statusFilter === 'all' || request.status === statusFilter
  );

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-amber-100 text-amber-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityBadgeClass = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-gray-100 text-gray-800';
      case 'medium':
        return 'bg-blue-100 text-blue-800';
      case 'high':
        return 'bg-amber-100 text-amber-800';
      case 'emergency':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getRequestTypeLabel = (type: string) => {
    const typeMap: Record<string, string> = {
      'plumbing': 'Plumbing Issue',
      'electrical': 'Electrical Problem',
      'aircon': 'Air Conditioning',
      'furniture': 'Furniture Repair',
      'cleaning': 'Cleaning Service',
      'internet': 'Internet Connection',
      'other': 'Other',
    };
    return typeMap[type] || type;
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 className="text-xl font-semibold">Service Request History</h2>
        <div className="mt-3 md:mt-0">
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Requests</option>
            <option value="pending">Pending</option>
            <option value="in-progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      {filteredRequests.length === 0 ? (
        <div className="text-center py-8">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <p className="mt-2 text-gray-500">No service requests found</p>
          {statusFilter !== 'all' && (
            <p className="text-sm text-gray-400">Try changing your filter</p>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredRequests.map((request) => (
            <div 
              key={request.id} 
              className="border border-gray-200 rounded-lg overflow-hidden"
            >
              <div 
                className="flex flex-col md:flex-row md:items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
                onClick={() => setExpandedRequestId(expandedRequestId === request.id ? null : request.id)}
              >
                <div className="flex-1">
                  <div className="flex flex-col md:flex-row md:items-center gap-2 mb-2">
                    <h3 className="font-medium text-gray-900">{getRequestTypeLabel(request.type)}</h3>
                    <div className="flex gap-2">
                      <span className={`px-2 py-1 inline-flex text-xs leading-4 font-medium rounded-full ${getStatusBadgeClass(request.status)}`}>
                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                      </span>
                      <span className={`px-2 py-1 inline-flex text-xs leading-4 font-medium rounded-full ${getPriorityBadgeClass(request.priority)}`}>
                        {request.priority.charAt(0).toUpperCase() + request.priority.slice(1)} Priority
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500 line-clamp-1">{request.description}</p>
                </div>
                <div className="flex items-center mt-2 md:mt-0">
                  <span className="text-xs text-gray-500 mr-2">
                    {formatDate(request.createdAt)}
                  </span>
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    className={`h-5 w-5 text-gray-400 transition-transform ${expandedRequestId === request.id ? 'transform rotate-180' : ''}`} 
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
              
              {expandedRequestId === request.id && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="border-t border-gray-200 p-4 bg-gray-50"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-1">Description</h4>
                      <p className="text-sm text-gray-600">{request.description}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-1">Request Details</h4>
                      <div className="space-y-1">
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Submitted:</span> {formatDate(request.createdAt)}
                        </p>
                        {request.scheduledDate && (
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Scheduled:</span> {formatDate(request.scheduledDate)}
                          </p>
                        )}
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Last Updated:</span> {formatDate(request.updatedAt)}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  {request.adminResponse && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <h4 className="text-sm font-medium text-blue-700 mb-1">Admin Response</h4>
                      <p className="text-sm text-blue-600">{request.adminResponse}</p>
                    </div>
                  )}
                  
                  {request.status === 'pending' && (
                    <div className="mt-4 flex justify-end">
                      <button className="px-4 py-2 text-sm text-red-600 hover:text-red-800 transition">
                        Cancel Request
                      </button>
                    </div>
                  )}
                </motion.div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
