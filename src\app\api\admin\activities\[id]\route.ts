import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

// GET a specific activity by ID
async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const { id } = params;

    // Get the activity
    const activity = await prisma.activity.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        performer: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    if (!activity) {
      return NextResponse.json(
        { success: false, message: 'Activity not found' },
        { status: 404 }
      );
    }

    // Format the activity for the response
    const formattedActivity = {
      id: activity.id,
      user: activity.user.name,
      userId: activity.userId,
      userRole: activity.user.role,
      performer: activity.performer?.name,
      performerId: activity.performerId,
      action: activity.action,
      details: activity.details,
      target: activity.target,
      targetId: activity.targetId,
      targetType: activity.targetType,
      ipAddress: activity.ipAddress,
      timestamp: activity.timestamp,
    };

    return NextResponse.json({
      success: true,
      data: formattedActivity,
    });
  } catch (error) {
    console.error(`Error fetching activity with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch activity' },
      { status: 500 }
    );
  }
}

// DELETE an activity
async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const { id } = params;

    // Check if the activity exists
    const activity = await prisma.activity.findUnique({
      where: { id },
    });

    if (!activity) {
      return NextResponse.json(
        { success: false, message: 'Activity not found' },
        { status: 404 }
      );
    }

    // Delete the activity
    await prisma.activity.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: 'Activity deleted successfully',
    });
  } catch (error) {
    console.error(`Error deleting activity with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete activity' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedGET = withAuth(GET, ['ADMIN']);
const protectedDELETE = withAuth(DELETE, ['ADMIN']);

export { protectedGET as GET, protectedDELETE as DELETE };
