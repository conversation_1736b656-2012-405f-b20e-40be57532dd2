"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ServiceRequestForm from '@/components/dashboard/ServiceRequestForm';
import ServiceRequestHistory from '@/components/dashboard/ServiceRequestHistory';
import { useAuth } from '@/context/AuthContext';
import Swal from 'sweetalert2';

// Define the service request type based on the API response
interface ServiceRequestResponse {
  id: string;
  type: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  responses?: {
    id: string;
    message: string;
    createdAt: string;
  }[];
  room?: {
    roomNumber: string;
  };
}

// Map API response to our UI format
interface ServiceRequest {
  id: string;
  type: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'emergency';
  createdAt: string;
  updatedAt: string;
  adminResponse?: string;
  scheduledDate?: string;
}

export default function ServiceRequestsPage() {
  const { user, isLoading } = useAuth();
  const [showNewRequestForm, setShowNewRequestForm] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<string | null>(null);
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([]);
  const [isLoadingRequests, setIsLoadingRequests] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch service requests from the API
  useEffect(() => {
    const fetchServiceRequests = async () => {
      if (isLoading) return; // Wait for auth to load
      if (!user) {
        setError('You must be logged in to view service requests');
        setIsLoadingRequests(false);
        return;
      }

      try {
        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication token not found');
          setIsLoadingRequests(false);
          return;
        }

        // Use the new dashboard API endpoint
        const response = await fetch('/api/dashboard/service-requests', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch service requests');
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || 'Failed to fetch service requests');
        }

        // Map API response to our UI format
        const mappedRequests: ServiceRequest[] = data.data.requests.map((req: ServiceRequestResponse) => {
          // Map status from API format to UI format
          let uiStatus: 'pending' | 'in-progress' | 'completed' | 'cancelled';
          switch (req.status) {
            case 'PENDING': uiStatus = 'pending'; break;
            case 'IN_PROGRESS': uiStatus = 'in-progress'; break;
            case 'RESOLVED': uiStatus = 'completed'; break;
            case 'CANCELLED': uiStatus = 'cancelled'; break;
            default: uiStatus = 'pending';
          }

          // Map priority from API format to UI format
          let uiPriority: 'low' | 'medium' | 'high' | 'emergency';
          switch (req.priority) {
            case 'LOW': uiPriority = 'low'; break;
            case 'MEDIUM': uiPriority = 'medium'; break;
            case 'HIGH': uiPriority = 'high'; break;
            case 'URGENT': uiPriority = 'emergency'; break;
            default: uiPriority = 'medium';
          }

          return {
            id: req.id,
            type: req.type.toLowerCase(),
            description: req.description,
            status: uiStatus,
            priority: uiPriority,
            createdAt: req.createdAt,
            updatedAt: req.updatedAt,
            adminResponse: req.adminResponse,
            scheduledDate: req.resolvedAt
          };
        });

        setServiceRequests(mappedRequests);
      } catch (err) {
        console.error('Error fetching service requests:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching service requests');
      } finally {
        setIsLoadingRequests(false);
      }
    };

    fetchServiceRequests();
  }, [user, isLoading]);

  const handleRequestSubmitted = () => {
    setShowNewRequestForm(false);
    // Refresh the service requests list
    setIsLoadingRequests(true);
    setError(null);

    // Wait a moment for the server to process the new request
    setTimeout(() => {
      const fetchServiceRequests = async () => {
        try {
          const token = localStorage.getItem('token');
          if (!token || !user) return;

          // Use the new dashboard API endpoint
          const response = await fetch('/api/dashboard/service-requests', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (!response.ok) {
            throw new Error('Failed to refresh service requests');
          }

          const data = await response.json();

          if (!data.success) {
            throw new Error(data.message || 'Failed to refresh service requests');
          }

          // Map API response to our UI format (same mapping as above)
          const mappedRequests: ServiceRequest[] = data.data.requests.map((req: ServiceRequestResponse) => {
            let uiStatus: 'pending' | 'in-progress' | 'completed' | 'cancelled';
            switch (req.status) {
              case 'PENDING': uiStatus = 'pending'; break;
              case 'IN_PROGRESS': uiStatus = 'in-progress'; break;
              case 'RESOLVED': uiStatus = 'completed'; break;
              case 'CANCELLED': uiStatus = 'cancelled'; break;
              default: uiStatus = 'pending';
            }

            let uiPriority: 'low' | 'medium' | 'high' | 'emergency';
            switch (req.priority) {
              case 'LOW': uiPriority = 'low'; break;
              case 'MEDIUM': uiPriority = 'medium'; break;
              case 'HIGH': uiPriority = 'high'; break;
              case 'URGENT': uiPriority = 'emergency'; break;
              default: uiPriority = 'medium';
            }

            return {
              id: req.id,
              type: req.type.toLowerCase(),
              description: req.description,
              status: uiStatus,
              priority: uiPriority,
              createdAt: req.createdAt,
              updatedAt: req.updatedAt,
              adminResponse: req.adminResponse,
              scheduledDate: req.resolvedAt
            };
          });

          setServiceRequests(mappedRequests);
        } catch (err) {
          console.error('Error refreshing service requests:', err);
          Swal.fire({
            title: 'Error',
            text: 'Failed to refresh service requests. Please reload the page.',
            icon: 'error',
            confirmButtonColor: '#f59e0b',
          });
        } finally {
          setIsLoadingRequests(false);
        }
      };

      fetchServiceRequests();
    }, 1000);
  };

  const handleViewRequest = (id: string) => {
    setSelectedRequest(id);
  };

  const handleCloseRequestDetails = () => {
    setSelectedRequest(null);
  };

  const selectedRequestData = selectedRequest
    ? serviceRequests.find(req => req.id === selectedRequest)
    : null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="space-y-6">
        {showNewRequestForm ? (
          <ServiceRequestForm
            roomNumber="A-101"
            onRequestSubmitted={handleRequestSubmitted}
            onCancel={() => setShowNewRequestForm(false)}
          />
        ) : selectedRequest ? (
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Request Details</h2>
              <button
                onClick={handleCloseRequestDetails}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {selectedRequestData && (
              <div className="space-y-6">
                <div className="flex justify-between">
                  <div>
                    <p className="text-sm text-gray-500">Request ID</p>
                    <p className="font-medium">{selectedRequestData.id}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      selectedRequestData.status === 'completed' ? 'bg-green-100 text-green-800' :
                      selectedRequestData.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                      'bg-amber-100 text-amber-800'
                    }`}>
                      {selectedRequestData.status.charAt(0).toUpperCase() + selectedRequestData.status.slice(1).replace('-', ' ')}
                    </span>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-500">Type</p>
                  <p className="font-medium">{selectedRequestData.type.charAt(0).toUpperCase() + selectedRequestData.type.slice(1)}</p>
                </div>

                <div>
                  <p className="text-sm text-gray-500">Description</p>
                  <p className="mt-1">{selectedRequestData.description}</p>
                </div>

                <div className="flex justify-between">
                  <div>
                    <p className="text-sm text-gray-500">Created</p>
                    <p className="font-medium">{new Date(selectedRequestData.createdAt).toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Last Updated</p>
                    <p className="font-medium">{new Date(selectedRequestData.updatedAt).toLocaleString()}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-500">Priority</p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    selectedRequestData.priority === 'high' ? 'bg-red-100 text-red-800' :
                    selectedRequestData.priority === 'medium' ? 'bg-amber-100 text-amber-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {selectedRequestData.priority.charAt(0).toUpperCase() + selectedRequestData.priority.slice(1)}
                  </span>
                </div>

                {selectedRequestData.adminResponse && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-500 mb-2">Admin Response</p>
                    <p className="text-gray-800">{selectedRequestData.adminResponse}</p>
                  </div>
                )}

                {selectedRequestData.scheduledDate && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm text-blue-500 mb-2">Scheduled Date</p>
                    <p className="text-blue-800 font-medium">{new Date(selectedRequestData.scheduledDate).toLocaleString()}</p>
                  </div>
                )}

                {selectedRequestData.status !== 'completed' && (
                  <div className="pt-4 border-t border-gray-200">
                    <h3 className="text-lg font-medium mb-3">Add a Response</h3>
                    <textarea
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                      rows={3}
                      placeholder="Type your message here..."
                    ></textarea>
                    <button className="mt-3 px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition">
                      Send Message
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          <>
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Service Requests</h2>
                <button
                  onClick={() => setShowNewRequestForm(true)}
                  className="px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  New Request
                </button>
              </div>

              <div className="space-y-4">
                {serviceRequests.map((request) => (
                  <div
                    key={request.id}
                    className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition cursor-pointer"
                    onClick={() => handleViewRequest(request.id)}
                  >
                    <div className="flex justify-between">
                      <div className="flex items-center">
                        <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                          request.status === 'completed' ? 'bg-green-500' :
                          request.status === 'in-progress' ? 'bg-blue-500' :
                          'bg-amber-500'
                        }`}></span>
                        <span className="font-medium">{request.type.charAt(0).toUpperCase() + request.type.slice(1)}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          request.priority === 'high' ? 'bg-red-100 text-red-800' :
                          request.priority === 'medium' ? 'bg-amber-100 text-amber-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {request.priority.charAt(0).toUpperCase() + request.priority.slice(1)}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          request.status === 'completed' ? 'bg-green-100 text-green-800' :
                          request.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                          'bg-amber-100 text-amber-800'
                        }`}>
                          {request.status.charAt(0).toUpperCase() + request.status.slice(1).replace('-', ' ')}
                        </span>
                      </div>
                    </div>
                    <p className="mt-2 text-sm text-gray-600">{request.description}</p>
                    <div className="mt-2 flex justify-between items-center">
                      <span className="text-xs text-gray-500">
                        Created: {new Date(request.createdAt).toLocaleDateString()}
                      </span>
                      {request.adminResponse && (
                        <span className="text-xs text-blue-600">Has response</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <h2 className="text-xl font-semibold mb-4">Request Types</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition cursor-pointer">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <span className="font-medium">Air Conditioning</span>
                  </div>
                  <p className="text-sm text-gray-600">Issues with cooling, heating, or strange noises from your AC unit.</p>
                </div>

                <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition cursor-pointer">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="font-medium">Plumbing</span>
                  </div>
                  <p className="text-sm text-gray-600">Water leaks, clogged drains, or issues with water pressure.</p>
                </div>

                <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition cursor-pointer">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span className="font-medium">Electrical</span>
                  </div>
                  <p className="text-sm text-gray-600">Power outages, faulty outlets, or lighting problems.</p>
                </div>

                <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition cursor-pointer">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <span className="font-medium">Furniture</span>
                  </div>
                  <p className="text-sm text-gray-600">Broken furniture, missing items, or replacement requests.</p>
                </div>

                <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition cursor-pointer">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span className="font-medium">Cleaning</span>
                  </div>
                  <p className="text-sm text-gray-600">Requests for room cleaning or issues with cleanliness.</p>
                </div>

                <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition cursor-pointer">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <span className="font-medium">Other</span>
                  </div>
                  <p className="text-sm text-gray-600">Any other issues not covered by the categories above.</p>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </motion.div>
  );
}
