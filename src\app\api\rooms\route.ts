import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET all rooms (with optional filtering)
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const roomTypeId = searchParams.get('roomTypeId');
    const available = searchParams.get('available');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build the where clause based on query parameters
    const where: any = {};

    if (roomTypeId) {
      where.roomTypeId = roomTypeId;
    }

    if (available === 'true') {
      where.isOccupied = false;
    } else if (available === 'false') {
      where.isOccupied = true;
    }

    // Get rooms and count
    const [rooms, totalCount] = await Promise.all([
      prisma.room.findMany({
        where,
        include: {
          roomType: true,
          images: true,
          residents: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: {
          roomNumber: 'asc'
        }
      }),
      prisma.room.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: rooms,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching rooms:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch rooms' },
      { status: 500 }
    );
  }
}

// POST create a new room
async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { roomNumber, roomTypeId, monthlyRate, isOccupied = false, description } = body;

    // Validate required fields
    if (!roomNumber || !roomTypeId) {
      return NextResponse.json(
        { success: false, message: 'Room number and room type are required' },
        { status: 400 }
      );
    }

    // Check if room with this number already exists
    const existingRoom = await prisma.room.findUnique({
      where: { roomNumber }
    });

    if (existingRoom) {
      return NextResponse.json(
        { success: false, message: 'Room with this number already exists' },
        { status: 409 }
      );
    }

    // Check if the room type exists
    const roomType = await prisma.roomType.findUnique({
      where: { id: roomTypeId }
    });

    if (!roomType) {
      return NextResponse.json(
        { success: false, message: 'Room type not found' },
        { status: 404 }
      );
    }

    // Create the room
    const room = await prisma.room.create({
      data: {
        roomNumber,
        roomTypeId,
        monthlyRate: monthlyRate || roomType.price,
        isOccupied,
        description
      },
      include: {
        roomType: true
      }
    });

    return NextResponse.json(
      {
        success: true,
        message: 'Room created successfully',
        data: room
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating room:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create room' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware to POST
const protectedPOST = withAuth(POST, ['ADMIN']);

export { protectedPOST as POST };
