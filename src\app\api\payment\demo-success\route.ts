import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

// This is a demo endpoint to simulate successful payment without using Stripe
async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { contractId, userId, roomId, rentAmount, depositAmount } = body;

    // Get the authenticated user from the request
    const user = (request as any).user;

    // Validate required fields
    if (!contractId || !userId || !roomId || !rentAmount) {
      return NextResponse.json(
        { success: false, message: 'Required fields are missing' },
        { status: 400 }
      );
    }

    // Verify that the user is authorized
    if (userId !== user.id && user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Update the contract to active
    await prisma.contract.update({
      where: { id: contractId },
      data: { isActive: true }
    });

    // Update the user role to RESIDENT
    await prisma.user.update({
      where: { id: userId },
      data: { role: 'RESIDENT' }
    });

    // Update the room to occupied and assign the user
    await prisma.room.update({
      where: { id: roomId },
      data: {
        isOccupied: true,
        residents: {
          connect: { id: userId }
        }
      }
    });

    // Create payment records
    // First month rent
    const rentPayment = await prisma.payment.create({
      data: {
        amount: rentAmount,
        dueDate: new Date(),
        paidDate: new Date(),
        status: 'PAID',
        type: 'RENT',
        notes: 'First month rent payment (Demo)',
        receiptNumber: `RCT-${new Date().getFullYear()}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
        paymentMethod: 'Demo Payment',
        userId,
        roomId
      }
    });

    // Security deposit
    const depositPayment = await prisma.payment.create({
      data: {
        amount: depositAmount || rentAmount * 2,
        dueDate: new Date(),
        paidDate: new Date(),
        status: 'PAID',
        type: 'DEPOSIT',
        notes: 'Security deposit payment (Demo)',
        receiptNumber: `RCT-${new Date().getFullYear()}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
        paymentMethod: 'Demo Payment',
        userId,
        roomId
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Demo payment processed successfully',
      data: {
        rentPayment,
        depositPayment
      }
    });
  } catch (error) {
    console.error('Error processing demo payment:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process demo payment' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedPOST = withAuth(POST);

export { protectedPOST as POST };
