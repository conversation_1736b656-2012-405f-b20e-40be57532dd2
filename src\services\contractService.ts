// Contract service for handling contract-related operations

// Get all contracts with optional filtering
export async function getContracts(params?: {
  userId?: string;
  roomId?: string;
  isActive?: boolean;
}) {
  const queryParams = new URLSearchParams();
  
  if (params?.userId) {
    queryParams.append('userId', params.userId);
  }
  
  if (params?.roomId) {
    queryParams.append('roomId', params.roomId);
  }
  
  if (params?.isActive !== undefined) {
    queryParams.append('isActive', params.isActive.toString());
  }
  
  const response = await fetch(`/api/contracts?${queryParams.toString()}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }
  });
  
  return response.json();
}

// Get a specific contract by ID
export async function getContractById(id: string) {
  const response = await fetch(`/api/contracts/${id}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }
  });
  
  return response.json();
}

// Create a new contract
export async function createContract(data: {
  roomId: string;
  startDate: string;
  endDate: string;
  rentAmount: number;
  depositAmount?: number;
}) {
  const response = await fetch('/api/contracts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify(data)
  });
  
  return response.json();
}

// Update an existing contract
export async function updateContract(id: string, data: {
  startDate?: string;
  endDate?: string;
  rentAmount?: number;
  depositAmount?: number;
  isActive?: boolean;
  terminationDate?: string;
  terminationReason?: string;
  documentUrl?: string;
}) {
  const response = await fetch(`/api/contracts/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify(data)
  });
  
  return response.json();
}

// Delete a contract
export async function deleteContract(id: string) {
  const response = await fetch(`/api/contracts/${id}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }
  });
  
  return response.json();
}
