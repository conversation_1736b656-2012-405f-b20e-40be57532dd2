"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import UtilityTracker from '@/components/dashboard/UtilityTracker';

interface UtilityReading {
  current: number;
  previous: number;
  usage: number;
  rate: number;
  cost: number;
  history: { month: string; usage: number }[];
}

interface UtilitiesData {
  water: UtilityReading;
  electricity: UtilityReading;
  totalCost: number;
  readingDate: Date;
}

export default function UtilitiesPage() {
  const { user } = useAuth();
  const [utilitiesData, setUtilitiesData] = useState<UtilitiesData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUtilitiesData = async () => {
      if (!user) return;

      try {
        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication token not found. Please log in again.');
          setIsLoading(false);
          return;
        }

        const response = await fetch('/api/dashboard/utilities', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch utilities data: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
          setUtilitiesData(result.data);
        } else {
          setError(result.message || 'Failed to fetch utilities data');
        }
      } catch (err) {
        console.error('Error fetching utilities data:', err);
        setError('An error occurred while fetching utilities data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUtilitiesData();
  }, [user]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
        <p>{error}</p>
      </div>
    );
  }

  // No data state
  if (!utilitiesData) {
    return (
      <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
        <p>No utility data available. Please try again later.</p>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="grid grid-cols-1 gap-6">
        {/* Summary Card */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold mb-4">Utility Summary</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-600 font-medium">Water</p>
              <p className="text-2xl font-bold text-blue-700">฿{utilitiesData.water.cost.toLocaleString()}</p>
              <p className="text-xs text-blue-600">{utilitiesData.water.usage.toFixed(1)} m³ used</p>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg">
              <p className="text-sm text-amber-600 font-medium">Electricity</p>
              <p className="text-2xl font-bold text-amber-700">฿{utilitiesData.electricity.cost.toLocaleString()}</p>
              <p className="text-xs text-amber-600">{utilitiesData.electricity.usage.toFixed(1)} kWh used</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <p className="text-sm text-green-600 font-medium">Total</p>
              <p className="text-2xl font-bold text-green-700">฿{utilitiesData.totalCost.toLocaleString()}</p>
              <p className="text-xs text-green-600">Last reading: {new Date(utilitiesData.readingDate).toLocaleDateString()}</p>
            </div>
          </div>
        </div>

        {/* Water Usage */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold mb-6">Water Usage</h2>
          <UtilityTracker
            type="water"
            current={utilitiesData.water.current}
            previous={utilitiesData.water.previous}
            rate={utilitiesData.water.rate}
            compact={false}
          />

          <div className="mt-8">
            <h3 className="text-lg font-medium mb-4">Monthly Usage History</h3>
            <div className="h-64 relative">
              <div className="flex items-end justify-between h-48 relative">
                {utilitiesData.water.history.map((item, index) => (
                  <div key={index} className="flex flex-col items-center w-1/6">
                    <div
                      className="bg-blue-500 w-12 rounded-t-md transition-all duration-500"
                      style={{
                        height: `${(item.usage / Math.max(...utilitiesData.water.history.map(h => h.usage))) * 100}%`,
                        minHeight: '10%'
                      }}
                    ></div>
                    <div className="text-xs text-gray-600 mt-2">{item.month}</div>
                    <div className="text-xs font-medium">{item.usage.toFixed(1)} m³</div>
                  </div>
                ))}
              </div>
              <div className="absolute left-0 right-0 bottom-16 border-t border-gray-200"></div>
            </div>
          </div>
        </div>

        {/* Electricity Usage */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold mb-6">Electricity Usage</h2>
          <UtilityTracker
            type="electricity"
            current={utilitiesData.electricity.current}
            previous={utilitiesData.electricity.previous}
            rate={utilitiesData.electricity.rate}
            compact={false}
          />

          <div className="mt-8">
            <h3 className="text-lg font-medium mb-4">Monthly Usage History</h3>
            <div className="h-64 relative">
              <div className="flex items-end justify-between h-48 relative">
                {utilitiesData.electricity.history.map((item, index) => (
                  <div key={index} className="flex flex-col items-center w-1/6">
                    <div
                      className="bg-amber-500 w-12 rounded-t-md transition-all duration-500"
                      style={{
                        height: `${(item.usage / Math.max(...utilitiesData.electricity.history.map(h => h.usage))) * 100}%`,
                        minHeight: '10%'
                      }}
                    ></div>
                    <div className="text-xs text-gray-600 mt-2">{item.month}</div>
                    <div className="text-xs font-medium">{item.usage.toFixed(1)} kWh</div>
                  </div>
                ))}
              </div>
              <div className="absolute left-0 right-0 bottom-16 border-t border-gray-200"></div>
            </div>
          </div>
        </div>

        {/* Tips for Reducing Utility Usage */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold mb-4">Tips for Reducing Utility Usage</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600">Water Conservation Tips</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Fix leaky faucets and toilets promptly</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Take shorter showers instead of baths</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Turn off the tap while brushing teeth or shaving</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Use a washing machine only with full loads</span>
                </li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium text-amber-600">Electricity Saving Tips</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="text-amber-500 mr-2">•</span>
                  <span>Turn off lights and appliances when not in use</span>
                </li>
                <li className="flex items-start">
                  <span className="text-amber-500 mr-2">•</span>
                  <span>Set air conditioner to 25°C for optimal efficiency</span>
                </li>
                <li className="flex items-start">
                  <span className="text-amber-500 mr-2">•</span>
                  <span>Use natural light during the day when possible</span>
                </li>
                <li className="flex items-start">
                  <span className="text-amber-500 mr-2">•</span>
                  <span>Unplug chargers when not in use to avoid phantom power</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
