import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user exists
    if (!user || !user.id) {
      return NextResponse.json(
        { success: false, message: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build the where clause
    const where: any = {
      userId: user.id
    };

    if (status) {
      where.status = status;
    }

    // Get total count for pagination
    const totalCount = await prisma.serviceRequest.count({ where });

    // Get service requests
    const serviceRequests = await prisma.serviceRequest.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        room: {
          select: {
            roomNumber: true,
            roomType: {
              select: {
                name: true
              }
            }
          }
        },
        responses: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 1 // Get only the latest response
        }
      },
      skip,
      take: limit
    });

    // Format the response
    const formattedRequests = serviceRequests.map(request => ({
      id: request.id,
      type: request.type,
      title: request.title,
      description: request.description,
      status: request.status,
      priority: request.priority,
      createdAt: request.createdAt,
      updatedAt: request.updatedAt,
      resolvedAt: request.resolvedAt,
      room: request.room ? {
        roomNumber: request.room.roomNumber,
        roomType: request.room.roomType.name
      } : null,
      adminResponse: request.responses.length > 0 ? request.responses[0].message : null
    }));

    // Get counts by status
    const pendingCount = await prisma.serviceRequest.count({
      where: {
        userId: user.id,
        status: 'PENDING'
      }
    });

    const inProgressCount = await prisma.serviceRequest.count({
      where: {
        userId: user.id,
        status: 'IN_PROGRESS'
      }
    });

    const resolvedCount = await prisma.serviceRequest.count({
      where: {
        userId: user.id,
        status: 'RESOLVED'
      }
    });

    const cancelledCount = await prisma.serviceRequest.count({
      where: {
        userId: user.id,
        status: 'CANCELLED'
      }
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);

    // Construct the response
    const serviceRequestData = {
      requests: formattedRequests,
      summary: {
        pending: pendingCount,
        inProgress: inProgressCount,
        resolved: resolvedCount,
        cancelled: cancelledCount,
        total: totalCount
      },
      pagination: {
        page,
        limit,
        totalPages,
        totalCount
      }
    };

    return NextResponse.json({
      success: true,
      data: serviceRequestData
    });
  } catch (error) {
    console.error('Error fetching service request data:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch service request data' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedGET = withAuth(GET, ['ADMIN', 'RESIDENT', 'VISITORS']);

export { protectedGET as GET };
