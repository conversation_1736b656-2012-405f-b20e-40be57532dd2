"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface RentalCountdownProps {
  startDate: string;
  endDate: string;
}

export default function RentalCountdown({ startDate, endDate }: RentalCountdownProps) {
  const [daysLeft, setDaysLeft] = useState(0);
  const [percentComplete, setPercentComplete] = useState(0);
  
  useEffect(() => {
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();
    const now = new Date().getTime();
    
    // Calculate days left
    const msLeft = end - now;
    const daysRemaining = Math.max(0, Math.ceil(msLeft / (1000 * 60 * 60 * 24)));
    setDaysLeft(daysRemaining);
    
    // Calculate percentage complete
    const totalDuration = end - start;
    const elapsed = now - start;
    const percent = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
    setPercentComplete(percent);
  }, [startDate, endDate]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col">
        <div className="flex justify-between mb-2">
          <div>
            <p className="text-sm text-gray-500">Start Date</p>
            <p className="font-medium">{new Date(startDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}</p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">End Date</p>
            <p className="font-medium">{new Date(endDate).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}</p>
          </div>
        </div>
        
        <div className="relative h-4 bg-gray-100 rounded-full overflow-hidden mt-2">
          <motion.div 
            className="absolute top-0 left-0 h-full bg-amber-500 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${percentComplete}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
          />
        </div>
        
        <div className="flex justify-between mt-2">
          <p className="text-sm text-gray-500">0%</p>
          <p className="text-sm text-gray-500">100%</p>
        </div>
      </div>
      
      <div className="flex justify-center">
        <div className="text-center">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-4xl font-bold text-amber-600"
          >
            {daysLeft}
          </motion.div>
          <p className="text-gray-500">days remaining</p>
        </div>
      </div>
      
      <div className="flex justify-between text-sm">
        <div>
          <p className="text-gray-500">Contract Length</p>
          <p className="font-medium">12 months</p>
        </div>
        <div className="text-right">
          <p className="text-gray-500">Completed</p>
          <p className="font-medium">{Math.round(percentComplete)}%</p>
        </div>
      </div>
    </div>
  );
}
