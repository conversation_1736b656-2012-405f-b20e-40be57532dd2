import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

async function GET(
  request: NextRequest,
  { params }: { params: { paymentId: string } }
) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user exists
    if (!user || !user.id) {
      return NextResponse.json(
        { success: false, message: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get the payment ID from the URL
    const { paymentId } = params;

    // Find the payment
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        room: {
          select: {
            id: true,
            roomNumber: true,
            roomType: {
              select: {
                name: true,
                price: true
              }
            }
          }
        }
      }
    });

    if (!payment) {
      return NextResponse.json(
        { success: false, message: 'Payment not found' },
        { status: 404 }
      );
    }

    // Verify that the payment belongs to the user or the user is an admin
    if (payment.userId !== user.id && user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Format the response
    const formattedPayment = {
      id: payment.id,
      amount: payment.amount,
      dueDate: payment.dueDate,
      paidDate: payment.paidDate,
      status: payment.status,
      type: payment.type,
      notes: payment.notes,
      receiptNumber: payment.receiptNumber,
      receiptUrl: payment.receiptUrl,
      paymentMethod: payment.paymentMethod,
      userId: payment.userId,
      roomId: payment.roomId,
      user: payment.user,
      room: payment.room,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt
    };

    return NextResponse.json({
      success: true,
      data: formattedPayment
    });
  } catch (error) {
    console.error('Error fetching payment:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch payment' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedGET = withAuth(GET, ['ADMIN', 'RESIDENT']);

export { protectedGET as GET };
