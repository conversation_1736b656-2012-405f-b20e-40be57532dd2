### Get all activities (admin)
GET http://localhost:3000/api/admin/activities
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE

### Get all activities with filtering
GET http://localhost:3000/api/admin/activities?targetType=USER&search=login&page=1&limit=10
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE

### Get a specific activity
GET http://localhost:3000/api/admin/activities/ACTIVITY_ID_HERE
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE

### Create a new activity
POST http://localhost:3000/api/admin/activities
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE

{
  "userId": "USER_ID_HERE",
  "action": "logged in",
  "details": "User logged in from IP ***********",
  "targetType": "SYSTEM",
  "ipAddress": "***********"
}

### Delete an activity (admin only)
DELETE http://localhost:3000/api/admin/activities/ACTIVITY_ID_HERE
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
