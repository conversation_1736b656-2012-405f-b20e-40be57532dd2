"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Swal from 'sweetalert2';
import ServiceResponseList from './ServiceResponseList';

// API response interface
interface ServiceRequestResponse {
  id: string;
  type: string;
  title: string;
  description: string;
  status: string; // 'PENDING', 'IN_PROGRESS', 'RESOLVED', 'CANCELLED'
  priority: string; // 'LOW', 'MEDIUM', 'HIGH', 'URGENT'
  userId: string;
  roomId: string;
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  room: {
    id: string;
    roomNumber: string;
    roomType: {
      name: string;
    };
  };
  responses: {
    id: string;
    message: string;
    createdAt: string;
    user?: {
      id: string;
      name: string;
      role: string;
    };
  }[];
  images?: {
    id: string;
    url: string;
  }[];
}

// UI interface
interface ServiceRequest {
  id: string;
  roomNumber: string;
  userName: string;
  type: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'emergency';
  createdAt: string;
  updatedAt: string;
  adminResponse?: string;
  scheduledDate?: string;
}

export default function ServiceRequestsManagement() {
  const [requests, setRequests] = useState<ServiceRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<ServiceRequest | null>(null);
  const [isResponseModalOpen, setIsResponseModalOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Function to map API response to UI format
  const mapServiceRequests = (apiRequests: ServiceRequestResponse[]): ServiceRequest[] => {
    return apiRequests.map(req => {
      // Get the latest response message for admin response
      const latestResponse = req.responses && req.responses.length > 0
        ? req.responses[0].message
        : undefined;

      // Map status from API format to UI format
      let uiStatus: 'pending' | 'in-progress' | 'completed' | 'cancelled';
      switch (req.status) {
        case 'PENDING': uiStatus = 'pending'; break;
        case 'IN_PROGRESS': uiStatus = 'in-progress'; break;
        case 'RESOLVED': uiStatus = 'completed'; break;
        case 'CANCELLED': uiStatus = 'cancelled'; break;
        default: uiStatus = 'pending';
      }

      // Map priority from API format to UI format
      let uiPriority: 'low' | 'medium' | 'high' | 'emergency';
      switch (req.priority) {
        case 'LOW': uiPriority = 'low'; break;
        case 'MEDIUM': uiPriority = 'medium'; break;
        case 'HIGH': uiPriority = 'high'; break;
        case 'URGENT': uiPriority = 'emergency'; break;
        default: uiPriority = 'medium';
      }

      return {
        id: req.id,
        roomNumber: req.room.roomNumber,
        userName: req.user.name,
        type: req.type.toLowerCase(),
        description: req.description,
        status: uiStatus,
        priority: uiPriority,
        createdAt: req.createdAt,
        updatedAt: req.updatedAt,
        adminResponse: latestResponse,
        scheduledDate: req.resolvedAt
      };
    });
  };

  // Fetch service requests from API
  const fetchServiceRequests = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch('/api/service-requests', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch service requests: ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch service requests');
      }

      const mappedRequests = mapServiceRequests(data.data);
      setRequests(mappedRequests);
    } catch (err) {
      console.error('Error fetching service requests:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while fetching service requests');
      // Clear any existing requests when there's an error
      setRequests([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchServiceRequests();
  }, []);

  // Filter requests based on filters and search term
  const filteredRequests = requests.filter(request => {
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || request.priority === priorityFilter;
    const matchesSearch =
      request.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.description.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesStatus && matchesPriority && matchesSearch;
  });

  const handleStatusChange = async (requestId: string, newStatus: ServiceRequest['status']) => {
    try {
      // Map UI status to API status
      let apiStatus: string;
      switch (newStatus) {
        case 'pending': apiStatus = 'PENDING'; break;
        case 'in-progress': apiStatus = 'IN_PROGRESS'; break;
        case 'completed': apiStatus = 'RESOLVED'; break;
        case 'cancelled': apiStatus = 'CANCELLED'; break;
        default: apiStatus = 'PENDING';
      }

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Call the API to update the status
      const response = await fetch(`/api/service-requests/${requestId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status: apiStatus })
      });

      if (!response.ok) {
        throw new Error(`Failed to update status: ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to update status');
      }

      // Show success message
      Swal.fire({
        title: 'Status Updated',
        text: `Request status has been updated to ${newStatus}`,
        icon: 'success',
        confirmButtonColor: '#f59e0b',
      });

      // Refresh the data to get the updated information from the server
      fetchServiceRequests();
    } catch (err) {
      console.error('Error updating status:', err);

      // Show detailed error message
      Swal.fire({
        title: 'Error Updating Status',
        text: err instanceof Error ? err.message : 'Failed to update status',
        icon: 'error',
        confirmButtonColor: '#f59e0b',
        footer: 'Please try again or contact the system administrator'
      });
    }
  };

  const handleDelete = async (requestId: string) => {
    // Show confirmation dialog before deleting
    const result = await Swal.fire({
      title: 'Delete Service Request',
      text: 'Are you sure you want to delete this service request? This action cannot be undone.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#f59e0b',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel'
    });

    // If user confirms deletion
    if (result.isConfirmed) {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('Authentication token not found');
        }

        // Call the API to delete the service request
        const response = await fetch(`/api/service-requests/${requestId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to delete service request: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || 'Failed to delete service request');
        }

        // Show success message
        Swal.fire({
          title: 'Deleted!',
          text: 'The service request has been deleted successfully.',
          icon: 'success',
          confirmButtonColor: '#f59e0b',
        });

        // Refresh the data to get the updated list
        fetchServiceRequests();
      } catch (err) {
        console.error('Error deleting service request:', err);

        // Show error message
        Swal.fire({
          title: 'Error',
          text: err instanceof Error ? err.message : 'Failed to delete service request',
          icon: 'error',
          confirmButtonColor: '#f59e0b',
          footer: 'Please try again or contact the system administrator'
        });
      }
    }
  };



  const openResponseModal = (request: ServiceRequest) => {
    setSelectedRequest(request);
    setIsResponseModalOpen(true);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-amber-100 text-amber-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityBadgeClass = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-gray-100 text-gray-800';
      case 'medium':
        return 'bg-blue-100 text-blue-800';
      case 'high':
        return 'bg-amber-100 text-amber-800';
      case 'emergency':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getRequestTypeLabel = (type: string) => {
    const typeMap: Record<string, string> = {
      'plumbing': 'Plumbing Issue',
      'electrical': 'Electrical Problem',
      'aircon': 'Air Conditioning',
      'furniture': 'Furniture Repair',
      'cleaning': 'Cleaning Service',
      'internet': 'Internet Connection',
      'other': 'Other',
    };
    return typeMap[type] || type;
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Service Requests</h1>
          <p className="text-gray-600">Manage and respond to resident service requests</p>
        </div>
        <button
          onClick={fetchServiceRequests}
          className="flex items-center px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition"
          disabled={isLoading}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 mr-2 ${isLoading ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {isLoading ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search by room, name, or description..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <span className="text-gray-500">Status:</span>
              <select
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="in-progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-500">Priority:</span>
              <select
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
              >
                <option value="all">All Priorities</option>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="emergency">Emergency</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Service Requests List */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
          <div className="flex">
            <div className="py-1">
              <svg className="h-6 w-6 text-red-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div>
              <p className="font-medium">Error loading service requests</p>
              <p className="text-sm">{error}</p>
              <p className="text-sm mt-1 text-red-600">
                Please make sure the API server is running and you have the correct permissions.
              </p>
              <button
                onClick={fetchServiceRequests}
                className="mt-2 text-sm bg-red-100 hover:bg-red-200 text-red-800 py-1 px-3 rounded transition"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      )}

      {isLoading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 animate-pulse">
              <div className="flex justify-between mb-4">
                <div className="h-6 bg-gray-200 rounded w-1/4"></div>
                <div className="h-6 bg-gray-200 rounded w-1/6"></div>
              </div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="flex justify-between">
                <div className="h-4 bg-gray-200 rounded w-1/5"></div>
                <div className="h-4 bg-gray-200 rounded w-1/5"></div>
              </div>
            </div>
          ))}
        </div>
      ) : filteredRequests.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-8 border border-gray-200 text-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <p className="mt-2 text-gray-500">No service requests found</p>
          {(statusFilter !== 'all' || priorityFilter !== 'all' || searchTerm) ? (
            <p className="text-sm text-gray-400 mt-1">Try changing your filters</p>
          ) : (
            <p className="text-sm text-gray-400 mt-1">There are no service requests in the system yet</p>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredRequests.map((request) => (
            <motion.div
              key={request.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white rounded-lg shadow-sm p-6 border border-gray-200"
            >
              <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                <div>
                  <div className="flex items-center mb-2">
                    <h3 className="text-lg font-medium text-gray-900 mr-3">{getRequestTypeLabel(request.type)}</h3>
                    <span className={`px-2 py-1 inline-flex text-xs leading-4 font-medium rounded-full ${getStatusBadgeClass(request.status)}`}>
                      {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                    </span>
                    <span className={`ml-2 px-2 py-1 inline-flex text-xs leading-4 font-medium rounded-full ${getPriorityBadgeClass(request.priority)}`}>
                      {request.priority.charAt(0).toUpperCase() + request.priority.slice(1)} Priority
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 mb-2">{request.description}</p>
                  <div className="flex flex-wrap gap-x-4 gap-y-1 text-xs text-gray-500">
                    <span>Room: <span className="font-medium">{request.roomNumber}</span></span>
                    <span>Resident: <span className="font-medium">{request.userName}</span></span>
                    <span>Submitted: <span className="font-medium">{formatDate(request.createdAt)}</span></span>
                    {request.scheduledDate && (
                      <span>Scheduled: <span className="font-medium">{formatDate(request.scheduledDate)}</span></span>
                    )}
                  </div>
                </div>
                <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
                  <button
                    onClick={() => openResponseModal(request)}
                    className="px-3 py-1 bg-amber-500 text-white rounded hover:bg-amber-600 transition"
                  >
                    {request.adminResponse ? 'Edit Response' : 'Respond'}
                  </button>
                  {request.status === 'pending' && (
                    <button
                      onClick={() => handleStatusChange(request.id, 'in-progress')}
                      className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
                    >
                      Start Progress
                    </button>
                  )}
                  {request.status === 'in-progress' && (
                    <button
                      onClick={() => handleStatusChange(request.id, 'completed')}
                      className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 transition"
                    >
                      Mark Completed
                    </button>
                  )}
                  {(request.status === 'pending' || request.status === 'in-progress') && (
                    <button
                      onClick={() => handleStatusChange(request.id, 'cancelled')}
                      className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition"
                    >
                      Cancel
                    </button>
                  )}
                  <button
                    onClick={() => handleDelete(request.id)}
                    className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 transition flex items-center"
                    title="Delete this service request"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Delete
                  </button>
                </div>
              </div>

              {request.adminResponse && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-700 mb-1">Admin Response</h4>
                  <p className="text-sm text-blue-600">{request.adminResponse}</p>
                </div>
              )}
            </motion.div>
          ))}
        </div>
      )}

      {/* Response Modal */}
      {isResponseModalOpen && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Respond to Request</h2>
                <button
                  onClick={() => setIsResponseModalOpen(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <h3 className="text-md font-medium text-gray-900 mr-3">{getRequestTypeLabel(selectedRequest.type)}</h3>
                  <span className={`px-2 py-1 inline-flex text-xs leading-4 font-medium rounded-full ${getPriorityBadgeClass(selectedRequest.priority)}`}>
                    {selectedRequest.priority.charAt(0).toUpperCase() + selectedRequest.priority.slice(1)} Priority
                  </span>
                </div>
                <p className="text-sm text-gray-500 mb-2">{selectedRequest.description}</p>
                <div className="flex flex-wrap gap-x-4 gap-y-1 text-xs text-gray-500">
                  <span>Room: <span className="font-medium">{selectedRequest.roomNumber}</span></span>
                  <span>Resident: <span className="font-medium">{selectedRequest.userName}</span></span>
                </div>
              </div>

              <div className="border-t border-gray-200 mt-4 pt-4">
                <ServiceResponseList
                  serviceRequestId={selectedRequest.id}
                  onResponseAdded={() => {
                    // Refresh the service requests list after a response is added
                    fetchServiceRequests();

                    // Close the modal after a short delay to show the success message
                    setTimeout(() => {
                      setIsResponseModalOpen(false);
                    }, 1500);
                  }}
                />
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}


