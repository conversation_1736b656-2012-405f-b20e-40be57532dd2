"use client";

import { motion } from 'framer-motion';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import RentalCountdown from '@/components/dashboard/RentalCountdown';
import UtilityTracker from '@/components/dashboard/UtilityTracker';
import { DashboardData } from '@/types/dashboard';

export default function UserDashboard() {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user) return;

      try {
        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication token not found. Please log in again.');
          setIsLoading(false);
          return;
        }

        const response = await fetch('/api/dashboard/overview', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch dashboard data: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
          setDashboardData(result.data);
        } else {
          setError(result.message || 'Failed to fetch dashboard data');
        }
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('An error occurred while fetching dashboard data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
        <p>{error}</p>
      </div>
    );
  }

  // No data state
  if (!dashboardData) {
    return (
      <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
        <p>No dashboard data available. Please try again later.</p>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Rental Countdown */}
        {dashboardData.contract && (
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <h2 className="text-xl font-semibold mb-4">Rental Period</h2>
            <RentalCountdown
              startDate={dashboardData.contract.startDate.toString()}
              endDate={dashboardData.contract.endDate.toString()}
            />
          </div>
        )}

        {/* Payment Summary */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold mb-4">Next Payment</h2>
          {dashboardData.nextPayment && dashboardData.nextPayment.status === 'PENDING' ? (
            <div className="flex flex-col space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Due Date:</span>
                <span className="font-medium">
                  {new Date(dashboardData.nextPayment.dueDate).toLocaleDateString('en-US', {
                    year: 'numeric', month: 'long', day: 'numeric'
                  })}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Amount:</span>
                <span className="font-medium">฿{dashboardData.nextPayment.amount.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Status:</span>
                <span className="px-3 py-1 rounded-full text-sm font-medium bg-amber-100 text-amber-800">
                  Pending
                </span>
              </div>
              <Link href={`/payment/invoice/${dashboardData.nextPayment.id}`} className="mt-2 w-full bg-amber-500 hover:bg-amber-600 text-white py-2 rounded-lg transition text-center">
                Pay Now
              </Link>
            </div>
          ) : (
            <div className="flex justify-center items-center py-6">
              <p className="text-gray-600 text-center">No pending payment</p>
            </div>
          )}
        </div>

        {/* Utility Usage Summary */}
        {dashboardData.utilities && (
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 lg:col-span-2">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Utility Usage</h2>
              <Link href="/user/dashboard/utilities" className="text-amber-600 hover:text-amber-700 text-sm font-medium">
                View Details
              </Link>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UtilityTracker
                type="water"
                current={dashboardData.utilities.water.current}
                previous={dashboardData.utilities.water.previous}
                rate={dashboardData.utilities.water.rate}
                compact={true}
              />
              <UtilityTracker
                type="electricity"
                current={dashboardData.utilities.electricity.current}
                previous={dashboardData.utilities.electricity.previous}
                rate={dashboardData.utilities.electricity.rate}
                compact={true}
              />
            </div>
          </div>
        )}

        {/* Recent Payments */}
        {dashboardData.payments && dashboardData.payments.length > 0 && (
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 lg:col-span-2">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Recent Payments</h2>
              <Link href="/user/dashboard/payments" className="text-amber-600 hover:text-amber-700 text-sm font-medium">
                View All
              </Link>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {dashboardData.payments.slice(0, 3).map((payment) => (
                    <tr key={payment.id}>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                        {new Date(payment.date).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                        {payment.type === 'RENT' ? 'Rent' :
                         payment.type === 'UTILITIES' ? 'Utilities' :
                         payment.type === 'DEPOSIT' ? 'Deposit' :
                         payment.type === 'MAINTENANCE' ? 'Maintenance' :
                         payment.type}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">฿{payment.amount.toLocaleString()}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          payment.status === 'PAID' ? 'bg-green-100 text-green-800' :
                          payment.status === 'PENDING' ? 'bg-amber-100 text-amber-800' :
                          payment.status === 'OVERDUE' ? 'bg-red-100 text-red-800' :
                          payment.status === 'UPCOMING' ? 'bg-gray-100 text-gray-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {payment.status === 'PAID' ? 'Paid' :
                           payment.status === 'PENDING' ? 'Pending' :
                           payment.status === 'OVERDUE' ? 'Overdue' :
                           payment.status === 'UPCOMING' ? 'Upcoming' :
                           payment.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Recent Service Requests */}
        {dashboardData.serviceRequests && dashboardData.serviceRequests.length > 0 && (
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 lg:col-span-2">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Recent Service Requests</h2>
              <Link href="/user/dashboard/service-requests" className="text-amber-600 hover:text-amber-700 text-sm font-medium">
                View All
              </Link>
            </div>
            <div className="space-y-4">
              {dashboardData.serviceRequests.slice(0, 2).map((request) => (
                <div key={request.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between">
                    <div className="flex items-center">
                      <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                        request.status === 'RESOLVED' ? 'bg-green-500' :
                        request.status === 'IN_PROGRESS' ? 'bg-blue-500' :
                        request.status === 'PENDING' ? 'bg-amber-500' :
                        'bg-gray-500'
                      }`}></span>
                      <span className="font-medium">
                        {request.type.replace('_', ' ').charAt(0) + request.type.replace('_', ' ').slice(1).toLowerCase()}
                      </span>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      request.status === 'RESOLVED' ? 'bg-green-100 text-green-800' :
                      request.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                      request.status === 'PENDING' ? 'bg-amber-100 text-amber-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {request.status === 'RESOLVED' ? 'Resolved' :
                       request.status === 'IN_PROGRESS' ? 'In Progress' :
                       request.status === 'PENDING' ? 'Pending' :
                       request.status === 'CANCELLED' ? 'Cancelled' :
                       request.status}
                    </span>
                  </div>
                  <p className="mt-2 text-sm text-gray-600">{request.description}</p>
                  {request.adminResponse && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-lg text-sm">
                      <p className="font-medium text-gray-700">Admin Response:</p>
                      <p className="text-gray-600">{request.adminResponse}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* No Data Messages */}
        {(!dashboardData.contract && !dashboardData.nextPayment && !dashboardData.utilities &&
          (!dashboardData.payments || dashboardData.payments.length === 0) &&
          (!dashboardData.serviceRequests || dashboardData.serviceRequests.length === 0)) && (
          <div className="lg:col-span-2 bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
            <p>No dashboard data available yet. If you're a new user, please complete your registration and rent a room to see your dashboard information.</p>
          </div>
        )}
      </div>
    </motion.div>
  );
}
