import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET all room types
async function handleGet(_request: NextRequest) {
  try {
    const roomTypes = await prisma.roomType.findMany({
      include: {
        _count: {
          select: { rooms: true }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Transform the amenities string to array
    const transformedRoomTypes = roomTypes.map((roomType) => {
      return {
        ...roomType,
        amenities: roomType.amenities ? JSON.parse(roomType.amenities) : [],
        roomCount: roomType._count.rooms
      };
    });

    return NextResponse.json({
      success: true,
      data: transformedRoomTypes
    });
  } catch (error) {
    console.error('Error fetching room types:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch room types' },
      { status: 500 }
    );
  }
}

// POST create a new room type
async function handlePost(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, price, amenities, imageUrl, imageData, fileType, fileName } = body;

    // Validate required fields
    if (!name || !price) {
      return NextResponse.json(
        { success: false, message: 'Name and price are required' },
        { status: 400 }
      );
    }

    // Check if room type with this name already exists
    const existingRoomType = await prisma.roomType.findUnique({
      where: { name }
    });

    if (existingRoomType) {
      return NextResponse.json(
        { success: false, message: 'Room type with this name already exists' },
        { status: 409 }
      );
    }

    // Create the room type with image data directly stored in the RoomType model
    const roomType = await prisma.roomType.create({
      data: {
        name,
        description,
        price: parseFloat(price.toString()),
        amenities: Array.isArray(amenities) ? JSON.stringify(amenities) : null,
        imageUrl,
        imageData,
        fileType,
        fileName
      }
    });

    return NextResponse.json(
      {
        success: true,
        message: 'Room type created successfully',
        data: {
          ...roomType,
          amenities: amenities || []
        }
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating room type:', error);
    // Provide more detailed error message
    const errorMessage = error instanceof Error
      ? `Failed to create room type: ${error.message}`
      : 'Failed to create room type';

    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: 500 }
    );
  }
}

// Export the handlers with authentication middleware
export const GET = handleGet;
export const POST = withAuth(handlePost, ['ADMIN']);
