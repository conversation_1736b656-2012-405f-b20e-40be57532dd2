"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';

interface RoomData {
  id: string;
  roomNumber: string;
  monthlyRate: number;
  isOccupied: boolean;
  description: string;
  roomType: {
    id: string;
    name: string;
    description: string;
    price: number;
    amenities: string;
  };
  images: {
    id: string;
    url: string;
    imageData: string;
  }[];
}

export default function RoomDetailClient({ roomId }: { roomId: string }) {
  const router = useRouter();
  const { user } = useAuth();
  const [roomData, setRoomData] = useState<RoomData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRoomType, setIsRoomType] = useState(false);

  // Fetch room data when component mounts or ID changes
  useEffect(() => {
    const fetchRoomData = async () => {
      try {
        // First try to fetch as a room type
        const roomTypeResponse = await fetch(`/api/room-types/${roomId}`);
        const roomTypeResult = await roomTypeResponse.json();

        if (roomTypeResult.success) {
          // Create a mock room data from room type
          const roomType = roomTypeResult.data;
          console.log('Room type data:', {
            id: roomType.id,
            name: roomType.name,
            hasImageData: !!roomType.imageData,
            imageDataPrefix: roomType.imageData ? roomType.imageData.substring(0, 30) + '...' : 'none',
            fileType: roomType.fileType
          });
          setIsRoomType(true);

          // Create a sample room number based on the room type name
          const roomNumber = `${roomType.name.charAt(0).toUpperCase()}-101`;

          // Parse amenities if needed
          let amenitiesStr = '[]';
          if (typeof roomType.amenities === 'string') {
            amenitiesStr = roomType.amenities;
          } else if (Array.isArray(roomType.amenities)) {
            amenitiesStr = JSON.stringify(roomType.amenities);
          }

          // Create mock room data
          const mockRoomData: RoomData = {
            id: roomType.id,
            roomNumber: roomNumber,
            monthlyRate: roomType.price,
            isOccupied: false,
            description: roomType.description || '',
            roomType: {
              id: roomType.id,
              name: roomType.name,
              description: roomType.description || '',
              price: roomType.price,
              amenities: amenitiesStr
            },
            images: roomType.imageData ? [
              {
                id: 'mock-image-1',
                url: roomType.imageUrl || '',
                imageData: roomType.imageData.startsWith('data:') ? roomType.imageData : `data:${roomType.fileType || 'image/jpeg'};base64,${roomType.imageData}`
              }
            ] : []
          };

          setRoomData(mockRoomData);
          setIsLoading(false);
          return;
        }

        // If not found as room type, try to fetch as a specific room
        const roomResponse = await fetch(`/api/rooms/${roomId}`);
        const roomResult = await roomResponse.json();

        if (roomResult.success) {
          setRoomData(roomResult.data);
        } else {
          setError('Room not found. Please try another room.');
        }
      } catch (err) {
        setError('An error occurred while fetching room data');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRoomData();
  }, [roomId]);

  const handleApplyForRoom = () => {
    if (!user) {
      // Redirect to login page if user is not logged in
      router.push('/auth?redirect=' + encodeURIComponent(`/rooms/${roomId}`));
      return;
    }

    // Redirect to contract page with the room ID or room type ID
    if (isRoomType) {
      // For room types, we need to find an available room of this type first
      // For now, just pass the room type ID and handle it in the contract page
      router.push(`/contract/type/${roomId}`);
    } else {
      // For specific rooms, use the room ID directly
      router.push(`/contract/room/${roomId}`);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  if (error || !roomData) {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4">
        <div className="max-w-6xl mx-auto bg-white rounded-xl shadow-sm p-8 border border-gray-100">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-700">{error || 'Room not found'}</p>
          <button
            onClick={() => router.push('/rooms')}
            className="mt-6 bg-amber-500 text-white px-6 py-2 rounded-lg font-medium hover:bg-amber-600 transition"
          >
            Back to Rooms
          </button>
        </div>
      </div>
    );
  }

  // Parse amenities from string to array
  const amenities = roomData.roomType.amenities ?
    JSON.parse(roomData.roomType.amenities) :
    ['Fully furnished', 'High-speed internet'];

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-6xl mx-auto bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100">
        <div className="md:flex">
          <div className="md:w-1/2">
            <div className="h-64 md:h-full bg-gray-200 relative">
              {roomData.images && roomData.images.length > 0 ? (
                <img
                  src={
                    roomData.images[0].imageData && roomData.images[0].imageData.startsWith('data:')
                      ? roomData.images[0].imageData
                      : roomData.images[0].url || '/images/noobroom.jpg'
                  }
                  alt={roomData.roomType.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Fallback to default image if there's an error loading the image
                    e.currentTarget.src = '/images/noobroom.jpg';
                  }}
                />
              ) : (
                <div className="absolute inset-0 flex items-center justify-center text-gray-500">
                  Room Image Placeholder
                </div>
              )}
              <div className="absolute top-4 right-4">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  roomData.isOccupied
                    ? 'bg-red-100 text-red-800'
                    : 'bg-green-100 text-green-800'
                }`}>
                  {roomData.isOccupied ? 'Occupied' : 'Available'}
                </span>
              </div>
            </div>
          </div>
          <div className="md:w-1/2 p-8">
            <div className="flex justify-between items-start mb-2">
              <h1 className="text-3xl font-bold">{roomData.roomType.name} - {roomData.roomNumber}</h1>
            </div>
            <p className="text-2xl text-amber-500 font-bold mb-6">฿{roomData.monthlyRate.toLocaleString()}/month</p>

            <div className="bg-amber-50 border-l-4 border-amber-500 p-4 mb-6 rounded-r-lg">
              <p className="text-gray-700">{roomData.description || roomData.roomType.description}</p>
            </div>

            <h2 className="text-xl font-bold mb-4 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Features
            </h2>
            <ul className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-6">
              {amenities.map((feature: string, index: number) => (
                <li key={index} className="flex items-center text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  {feature}
                </li>
              ))}
            </ul>

            <button
              onClick={handleApplyForRoom}
              disabled={roomData.isOccupied}
              className={`px-6 py-3 rounded-lg font-medium w-full transition-all duration-300 ${
                roomData.isOccupied
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-amber-500 text-white hover:bg-amber-600 shadow-md hover:shadow-lg'
              }`}
            >
              {roomData.isOccupied ? 'Room Not Available' : 'Apply for this Room'}
            </button>

            <div className="mt-6 flex justify-center">
              <button
                onClick={() => router.push('/rooms')}
                className="text-amber-600 hover:text-amber-700 font-medium flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
                Back to All Rooms
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
