import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

async function handler(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Fetch the user from the database to get the latest data
    const dbUser = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        rooms: true, // Include rooms if needed
      },
    });

    if (!dbUser) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Remove sensitive information
    const { password, ...userWithoutPassword } = dbUser;

    return NextResponse.json(
      {
        success: true,
        user: userWithoutPassword,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json(
      { success: false, message: 'An error occurred while fetching user profile' },
      { status: 500 }
    );
  }
}

export const GET = withAuth(handler);
