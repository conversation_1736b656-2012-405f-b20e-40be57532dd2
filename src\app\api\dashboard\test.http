### Get dashboard overview data
GET http://localhost:3000/api/dashboard/overview
Authorization: Bearer YOUR_TOKEN_HERE

### Get payments data
GET http://localhost:3000/api/dashboard/payments
Authorization: Bearer YOUR_TOKEN_HERE

### Get utilities data
GET http://localhost:3000/api/dashboard/utilities
Authorization: Bearer YOUR_TOKEN_HERE

### Get service requests data
GET http://localhost:3000/api/dashboard/service-requests
Authorization: Bearer YOUR_TOKEN_HERE

### Get service requests with pagination and filtering
GET http://localhost:3000/api/dashboard/service-requests?status=PENDING&page=1&limit=5
Authorization: Bearer YOUR_TOKEN_HERE

### Get settings data
GET http://localhost:3000/api/dashboard/settings
Authorization: Bearer YOUR_TOKEN_HERE

### Update user profile
PUT http://localhost:3000/api/dashboard/settings
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "profile": {
    "name": "<PERSON>",
    "phone": "************",
    "address": "123 Main St, Bangkok, Thailand"
  }
}

### Update password
PUT http://localhost:3000/api/dashboard/settings
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "password": {
    "currentPassword": "oldpassword",
    "newPassword": "newpassword"
  }
}

### Update notification preferences
PUT http://localhost:3000/api/dashboard/settings
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "notifications": {
    "email": true,
    "sms": false,
    "app": true
  }
}
