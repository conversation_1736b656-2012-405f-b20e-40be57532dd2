// Activity Logger Service
// This service provides functions to log various activities in the system

export interface ActivityLogData {
  userId: string;
  user: string;
  userRole: "Admin" | "Staff" | "Resident";
  action: string;
  details?: string;
  target?: string;
  targetId?: string;
  targetType?: "User" | "Room" | "Payment" | "ServiceRequest" | "Utility" | "System";
  ipAddress?: string;
}

class ActivityLoggerService {
  private apiEndpoint = '/api/admin/activities';
  useApi = true; // Set to true to use API, false to use localStorage

  /**
   * Log an activity
   * @param data Activity data to log
   */
  async logActivity(data: ActivityLogData): Promise<void> {
    console.log('Activity logged:', data);

    if (this.useApi) {
      try {
        // Get token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          console.warn('No authentication token found, falling back to localStorage');
          this.fallbackToLocalStorage(data);
          return;
        }

        // Make API call to store activity
        const response = await fetch(this.apiEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(data)
        });

        if (!response.ok) {
          console.warn('Failed to log activity via API, falling back to localStorage');
          this.fallbackToLocalStorage(data);
        }
      } catch (error) {
        console.error('Error logging activity via API:', error);
        this.fallbackToLocalStorage(data);
      }
    } else {
      this.fallbackToLocalStorage(data);
    }
  }

  /**
   * Fallback to localStorage when API is not available
   */
  private fallbackToLocalStorage(data: ActivityLogData): void {
    const timestamp = new Date().toISOString();
    const activity = {
      id: Math.floor(Math.random() * 1000000), // Generate random ID
      timestamp,
      ...data
    };

    // Store in localStorage for demo purposes
    this.storeActivityInLocalStorage(activity);
  }

  /**
   * Log a user login
   */
  logLogin(userId: string, user: string, userRole: "Admin" | "Staff" | "Resident", ipAddress?: string): void {
    this.logActivity({
      userId,
      user,
      userRole,
      action: "logged in",
      targetType: "System",
      ipAddress
    });
  }

  /**
   * Log a user logout
   */
  logLogout(userId: string, user: string, userRole: "Admin" | "Staff" | "Resident", ipAddress?: string): void {
    this.logActivity({
      userId,
      user,
      userRole,
      action: "logged out",
      targetType: "System",
      ipAddress
    });
  }

  /**
   * Log a user creation
   */
  logUserCreation(
    adminId: string,
    admin: string,
    adminRole: "Admin" | "Staff",
    newUserId: string,
    newUser: string,
    details?: string
  ): void {
    this.logActivity({
      userId: adminId,
      user: admin,
      userRole: adminRole,
      action: "created a new user",
      details: details || `Created user account for ${newUser}`,
      target: newUser,
      targetId: newUserId,
      targetType: "User"
    });
  }

  /**
   * Log a user update
   */
  logUserUpdate(
    adminId: string,
    admin: string,
    adminRole: "Admin" | "Staff",
    userId: string,
    user: string,
    details: string
  ): void {
    this.logActivity({
      userId: adminId,
      user: admin,
      userRole: adminRole,
      action: "updated user",
      details,
      target: user,
      targetId: userId,
      targetType: "User"
    });
  }

  /**
   * Log a room assignment
   */
  logRoomAssignment(
    adminId: string,
    admin: string,
    adminRole: "Admin" | "Staff",
    userId: string,
    user: string,
    roomId: string,
    roomNumber: string
  ): void {
    this.logActivity({
      userId: adminId,
      user: admin,
      userRole: adminRole,
      action: "assigned room",
      details: `Assigned room ${roomNumber} to ${user}`,
      target: user,
      targetId: userId,
      targetType: "Room"
    });
  }

  /**
   * Log a payment record
   */
  logPaymentRecord(
    staffId: string,
    staff: string,
    staffRole: "Admin" | "Staff",
    userId: string,
    user: string,
    amount: number,
    paymentType: string
  ): void {
    this.logActivity({
      userId: staffId,
      user: staff,
      userRole: staffRole,
      action: "recorded payment",
      details: `Recorded ${paymentType} payment of ฿${amount.toLocaleString()}`,
      target: user,
      targetId: userId,
      targetType: "Payment"
    });
  }

  /**
   * Log a service request submission
   */
  logServiceRequest(
    userId: string,
    user: string,
    userRole: "Admin" | "Staff" | "Resident",
    requestId: string,
    details: string
  ): void {
    this.logActivity({
      userId,
      user,
      userRole,
      action: "submitted service request",
      details,
      targetId: requestId,
      targetType: "ServiceRequest"
    });
  }

  /**
   * Log a service request response
   */
  logServiceRequestResponse(
    staffId: string,
    staff: string,
    staffRole: "Admin" | "Staff",
    requestId: string,
    requestTitle: string,
    details: string
  ): void {
    this.logActivity({
      userId: staffId,
      user: staff,
      userRole: staffRole,
      action: "responded to service request",
      details,
      target: requestTitle,
      targetId: requestId,
      targetType: "ServiceRequest"
    });
  }

  /**
   * Log utility readings update
   */
  logUtilityReadings(
    staffId: string,
    staff: string,
    staffRole: "Admin" | "Staff",
    roomId: string,
    roomNumber: string,
    details: string
  ): void {
    this.logActivity({
      userId: staffId,
      user: staff,
      userRole: staffRole,
      action: "updated utility readings",
      details,
      target: `Room ${roomNumber}`,
      targetId: roomId,
      targetType: "Utility"
    });
  }

  /**
   * Log system settings update
   */
  logSystemUpdate(
    adminId: string,
    admin: string,
    adminRole: "Admin",
    details: string
  ): void {
    this.logActivity({
      userId: adminId,
      user: admin,
      userRole: adminRole,
      action: "updated system settings",
      details,
      targetType: "System"
    });
  }

  /**
   * Log report generation
   */
  logReportGeneration(
    adminId: string,
    admin: string,
    adminRole: "Admin" | "Staff",
    reportType: string
  ): void {
    this.logActivity({
      userId: adminId,
      user: admin,
      userRole: adminRole,
      action: "generated report",
      details: `Generated ${reportType} report`,
      targetType: "System"
    });
  }

  // Helper method to store activities in localStorage (for demo purposes only)
  private storeActivityInLocalStorage(activity: any): void {
    try {
      // Get existing activities
      const activitiesJson = localStorage.getItem('dormitory_activities');
      const activities = activitiesJson ? JSON.parse(activitiesJson) : [];

      // Add new activity
      activities.unshift(activity); // Add to beginning of array

      // Limit to 1000 activities
      const limitedActivities = activities.slice(0, 1000);

      // Save back to localStorage
      localStorage.setItem('dormitory_activities', JSON.stringify(limitedActivities));
    } catch (error) {
      console.error('Error storing activity in localStorage:', error);
    }
  }

  /**
   * Get activities from API or localStorage
   * @param filters Optional filters for the API request
   * @returns Promise with activities array
   */
  async getActivities(filters?: {
    page?: number;
    limit?: number;
    targetType?: string;
    search?: string;
    startDate?: string;
    endDate?: string;
    userId?: string;
  }): Promise<any> {
    if (this.useApi) {
      try {
        // Get token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          console.warn('No authentication token found, falling back to localStorage');
          return this.getActivitiesFromLocalStorage();
        }

        // Build query string from filters
        let queryString = '';
        if (filters) {
          const params = new URLSearchParams();
          if (filters.page) params.append('page', filters.page.toString());
          if (filters.limit) params.append('limit', filters.limit.toString());
          if (filters.targetType) params.append('targetType', filters.targetType);
          if (filters.search) params.append('search', filters.search);
          if (filters.startDate) params.append('startDate', filters.startDate);
          if (filters.endDate) params.append('endDate', filters.endDate);
          if (filters.userId) params.append('userId', filters.userId);

          queryString = `?${params.toString()}`;
        }

        // Make API call to get activities
        const response = await fetch(`${this.apiEndpoint}${queryString}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          console.warn('Failed to get activities from API, falling back to localStorage');
          return this.getActivitiesFromLocalStorage();
        }

        const data = await response.json();
        return data.data || [];
      } catch (error) {
        console.error('Error getting activities from API:', error);
        return this.getActivitiesFromLocalStorage();
      }
    } else {
      return this.getActivitiesFromLocalStorage();
    }
  }

  /**
   * Get activities from localStorage (fallback method)
   */
  private getActivitiesFromLocalStorage(): any[] {
    try {
      const activitiesJson = localStorage.getItem('dormitory_activities');
      return activitiesJson ? JSON.parse(activitiesJson) : [];
    } catch (error) {
      console.error('Error retrieving activities from localStorage:', error);
      return [];
    }
  }
}

// Create a singleton instance
const activityLogger = new ActivityLoggerService();

export default activityLogger;
