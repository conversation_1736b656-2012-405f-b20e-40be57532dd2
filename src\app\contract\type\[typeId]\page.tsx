"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';

interface RoomTypeData {
  id: string;
  name: string;
  description: string;
  price: number;
  amenities: string | string[];
  imageUrl?: string;
  imageData?: string;
}

interface AvailableRoom {
  id: string;
  roomNumber: string;
  monthlyRate: number;
  isOccupied: boolean;
  description: string;
}

export default function ContractByRoomTypePage({ params }: { params: { typeId: string } }) {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [roomTypeData, setRoomTypeData] = useState<RoomTypeData | null>(null);
  const [availableRooms, setAvailableRooms] = useState<AvailableRoom[]>([]);
  const [selectedRoomId, setSelectedRoomId] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  
  // Contract details
  const [startDate, setStartDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [endDate, setEndDate] = useState<string>(
    new Date(new Date().setFullYear(new Date().getFullYear() + 1))
      .toISOString()
      .split('T')[0]
  );

  useEffect(() => {
    // Redirect if user is not logged in
    if (!authLoading && !user) {
      router.push('/auth?redirect=' + encodeURIComponent(`/contract/type/${params.typeId}`));
      return;
    }

    const fetchData = async () => {
      try {
        // Fetch room type data
        const roomTypeResponse = await fetch(`/api/room-types/${params.typeId}`);
        const roomTypeResult = await roomTypeResponse.json();
        
        if (!roomTypeResult.success) {
          setError(roomTypeResult.message || 'Failed to fetch room type data');
          setIsLoading(false);
          return;
        }
        
        setRoomTypeData(roomTypeResult.data);
        
        // Fetch available rooms of this type
        const roomsResponse = await fetch(`/api/rooms?roomTypeId=${params.typeId}&available=true`);
        const roomsResult = await roomsResponse.json();
        
        if (roomsResult.success) {
          setAvailableRooms(roomsResult.data);
          
          // Auto-select the first available room if any
          if (roomsResult.data.length > 0) {
            setSelectedRoomId(roomsResult.data[0].id);
          } else {
            setError('No available rooms of this type');
          }
        } else {
          setError(roomsResult.message || 'Failed to fetch available rooms');
        }
      } catch (err) {
        setError('An error occurred while fetching data');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchData();
    }
  }, [params.typeId, user, authLoading, router]);

  const handleProceedToPayment = async () => {
    if (!user || !roomTypeData || !selectedRoomId) {
      setError('Please select a room to continue');
      return;
    }

    try {
      // Get the selected room details
      const selectedRoom = availableRooms.find(room => room.id === selectedRoomId);
      
      if (!selectedRoom) {
        setError('Selected room not found');
        return;
      }
      
      // Create contract
      const contractResponse = await fetch('/api/contracts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          roomId: selectedRoomId,
          startDate,
          endDate,
          rentAmount: selectedRoom.monthlyRate,
          depositAmount: selectedRoom.monthlyRate * 2 // 2 months deposit
        })
      });

      const contractResult = await contractResponse.json();
      
      if (contractResult.success) {
        // Redirect to payment page
        router.push(`/payment/${contractResult.data.id}`);
      } else {
        setError(contractResult.message || 'Failed to create contract');
      }
    } catch (err) {
      setError('An error occurred while creating the contract');
      console.error(err);
    }
  };

  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !roomTypeData) {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-8">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-700">{error || 'Room type not found'}</p>
          <button 
            onClick={() => router.push('/rooms')}
            className="mt-6 bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition"
          >
            Back to Rooms
          </button>
        </div>
      </div>
    );
  }

  // Parse amenities if needed
  let amenities: string[] = [];
  if (typeof roomTypeData.amenities === 'string') {
    try {
      amenities = JSON.parse(roomTypeData.amenities);
    } catch (e) {
      amenities = [roomTypeData.amenities];
    }
  } else if (Array.isArray(roomTypeData.amenities)) {
    amenities = roomTypeData.amenities;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-8">
        <h1 className="text-3xl font-bold mb-6 text-center">Room Rental Agreement</h1>
        
        <div className="mb-8 p-4 bg-blue-50 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Room Type Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-600">Room Type:</p>
              <p className="font-medium">{roomTypeData.name}</p>
            </div>
            <div>
              <p className="text-gray-600">Monthly Rent:</p>
              <p className="font-medium">฿{roomTypeData.price.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-gray-600">Security Deposit:</p>
              <p className="font-medium">฿{(roomTypeData.price * 2).toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Select Available Room</h2>
          {availableRooms.length > 0 ? (
            <div className="mb-4">
              <label className="block text-gray-700 mb-2">Room Number</label>
              <select
                value={selectedRoomId}
                onChange={(e) => setSelectedRoomId(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                {availableRooms.map(room => (
                  <option key={room.id} value={room.id}>
                    {room.roomNumber} - ฿{room.monthlyRate.toLocaleString()}/month
                  </option>
                ))}
              </select>
            </div>
          ) : (
            <p className="text-red-500">No available rooms of this type</p>
          )}
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Lease Term</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-gray-700 mb-2">Start Date</label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                min={new Date().toISOString().split('T')[0]}
              />
            </div>
            <div>
              <label className="block text-gray-700 mb-2">End Date</label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                min={startDate}
              />
            </div>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Terms and Conditions</h2>
          <div className="bg-gray-50 p-4 rounded-lg max-h-60 overflow-y-auto mb-4">
            <h3 className="font-semibold mb-2">1. PARTIES</h3>
            <p className="text-sm mb-3">
              This Residential Lease Agreement ("Agreement") is made between the Landlord and Tenant as specified above. The Landlord leases to Tenant and Tenant leases from Landlord, for residential purposes only, the premises as specified above ("Premises"), together with any and all appurtenances thereto, in accordance with the following terms and conditions.
            </p>
            
            <h3 className="font-semibold mb-2">2. TERM</h3>
            <p className="text-sm mb-3">
              The term of this Agreement shall be for the period specified above, commencing and ending on the dates specified above, unless sooner terminated as provided herein.
            </p>
            
            <h3 className="font-semibold mb-2">3. RENT</h3>
            <p className="text-sm mb-3">
              Tenant agrees to pay, without demand, to Landlord as rent for the Premises the amount specified above per month, on or before the first day of each calendar month, at Landlord's address as specified above, or at such other place as Landlord may designate in writing.
            </p>
            
            <h3 className="font-semibold mb-2">4. SECURITY DEPOSIT</h3>
            <p className="text-sm mb-3">
              Upon execution of this Agreement, Tenant shall deposit with Landlord the sum specified above as a security deposit. The security deposit shall be held by Landlord without liability for interest as security for the faithful performance by Tenant of all terms, covenants, and conditions of this Agreement.
            </p>
            
            <h3 className="font-semibold mb-2">5. UTILITIES</h3>
            <p className="text-sm mb-3">
              Tenant shall be responsible for the payment of all utilities and services to the Premises, except for those specifically assumed by Landlord as specified above.
            </p>
            
            <h3 className="font-semibold mb-2">6. USE OF PREMISES</h3>
            <p className="text-sm mb-3">
              The Premises shall be used and occupied by Tenant exclusively as a private residence and for no other purpose without the prior written consent of Landlord. Tenant shall not use the Premises, or permit the Premises to be used, in a manner that is unlawful, disruptive, or dangerous.
            </p>
            
            <h3 className="font-semibold mb-2">7. MAINTENANCE AND REPAIRS</h3>
            <p className="text-sm mb-3">
              Tenant shall maintain the Premises in a clean and sanitary condition and shall not damage or misuse the Premises. Tenant shall be responsible for the cost of repairing any damage to the Premises caused by Tenant or Tenant's guests.
            </p>
          </div>
          
          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="agree-terms"
              checked={agreedToTerms}
              onChange={(e) => setAgreedToTerms(e.target.checked)}
              className="mr-2"
            />
            <label htmlFor="agree-terms" className="text-gray-700">
              I have read and agree to the terms and conditions
            </label>
          </div>
        </div>

        <div className="flex justify-between">
          <button
            onClick={() => router.back()}
            className="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-400 transition"
          >
            Back
          </button>
          <button
            onClick={handleProceedToPayment}
            disabled={!agreedToTerms || !selectedRoomId || availableRooms.length === 0}
            className={`px-6 py-2 rounded-lg font-medium ${
              agreedToTerms && selectedRoomId && availableRooms.length > 0
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            } transition`}
          >
            Proceed to Payment
          </button>
        </div>
      </div>
    </div>
  );
}
