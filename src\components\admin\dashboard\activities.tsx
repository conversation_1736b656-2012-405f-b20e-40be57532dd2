"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import DataTable from "@/components/admin/DataTable";
import Modal from "@/components/admin/Modal";
import activityLogger from "@/services/activityLogger";

// Activity interface
interface Activity {
  id: number;
  user: string;
  userId: string;
  userRole: "Admin" | "Staff" | "Resident";
  action: string;
  details?: string;
  target?: string;
  targetId?: string;
  targetType?: "User" | "Room" | "Payment" | "ServiceRequest" | "Utility" | "System";
  timestamp: string;
  ipAddress?: string;
}

// Activity type for filtering
type ActivityType = "All" | "User" | "Room" | "Payment" | "ServiceRequest" | "Utility" | "System" | "Login";

export default function ActivitiesManagement() {
  const [isLoading, setIsLoading] = useState(true);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [filteredActivities, setFilteredActivities] = useState<Activity[]>([]);
  const [activityType, setActivityType] = useState<ActivityType>("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [dateRange, setDateRange] = useState({
    start: "",
    end: "",
  });

  // Modal states
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);

  // Mock data for activities
  const mockActivities: Activity[] = [
    {
      id: 1,
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      action: "logged in",
      timestamp: "2023-11-15T08:30:00Z",
      ipAddress: "***********",
      targetType: "System",
    },
    {
      id: 2,
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      action: "created a new user",
      details: "Created user account for John Doe",
      target: "John Doe",
      targetId: "user1",
      targetType: "User",
      timestamp: "2023-11-15T09:15:00Z",
      ipAddress: "***********",
    },
    {
      id: 3,
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      action: "assigned room",
      details: "Assigned room A-101 to John Doe",
      target: "John Doe",
      targetId: "user1",
      targetType: "Room",
      timestamp: "2023-11-15T09:20:00Z",
      ipAddress: "***********",
    },
    {
      id: 4,
      user: "Staff Member",
      userId: "staff1",
      userRole: "Staff",
      action: "recorded payment",
      details: "Recorded rent payment of ฿6,500",
      target: "John Doe",
      targetId: "user1",
      targetType: "Payment",
      timestamp: "2023-11-15T10:45:00Z",
      ipAddress: "***********",
    },
    {
      id: 5,
      user: "John Doe",
      userId: "user1",
      userRole: "Resident",
      action: "submitted service request",
      details: "AC not working properly",
      targetType: "ServiceRequest",
      timestamp: "2023-11-15T14:20:00Z",
      ipAddress: "***********",
    },
    {
      id: 6,
      user: "Staff Member",
      userId: "staff1",
      userRole: "Staff",
      action: "updated utility readings",
      details: "Updated water and electricity readings for Room A-101",
      target: "Room A-101",
      targetId: "room1",
      targetType: "Utility",
      timestamp: "2023-11-16T09:30:00Z",
      ipAddress: "***********",
    },
    {
      id: 7,
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      action: "generated report",
      details: "Generated monthly financial report",
      targetType: "System",
      timestamp: "2023-11-16T11:15:00Z",
      ipAddress: "***********",
    },
    {
      id: 8,
      user: "Sarah Smith",
      userId: "user2",
      userRole: "Resident",
      action: "logged in",
      targetType: "System",
      timestamp: "2023-11-16T13:45:00Z",
      ipAddress: "***********",
    },
    {
      id: 9,
      user: "Staff Member",
      userId: "staff1",
      userRole: "Staff",
      action: "responded to service request",
      details: "Scheduled maintenance for AC repair in Room A-101",
      target: "Service Request #5",
      targetId: "sr5",
      targetType: "ServiceRequest",
      timestamp: "2023-11-16T15:30:00Z",
      ipAddress: "***********",
    },
    {
      id: 10,
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      action: "updated system settings",
      details: "Changed payment due notification settings",
      targetType: "System",
      timestamp: "2023-11-17T10:00:00Z",
      ipAddress: "***********",
    },
  ];

  useEffect(() => {
    // Load activities from the activity logger service
    const fetchActivities = async () => {
      setIsLoading(true);
      try {
        // Try to get activities from the logger service first
        let loadedActivities = await activityLogger.getActivities();

        // If no activities found (first time use), use mock data
        if (!loadedActivities || loadedActivities.length === 0) {
          // Store mock activities in the logger for future use
          for (const activity of mockActivities) {
            const { id, ...activityData } = activity;
            await activityLogger.logActivity({
              userId: activity.userId,
              user: activity.user,
              userRole: activity.userRole,
              action: activity.action,
              details: activity.details,
              target: activity.target,
              targetId: activity.targetId,
              targetType: activity.targetType,
              ipAddress: activity.ipAddress
            });
          }

          // Reload activities after storing mocks
          loadedActivities = await activityLogger.getActivities();

          // If still no activities, use mock data directly
          if (!loadedActivities || loadedActivities.length === 0) {
            loadedActivities = mockActivities;
          }
        }

        setActivities(loadedActivities);
        setFilteredActivities(loadedActivities);
      } catch (error) {
        console.error('Error loading activities:', error);
        // Fallback to mock data on error
        setActivities(mockActivities);
        setFilteredActivities(mockActivities);
      } finally {
        setIsLoading(false);
      }
    };

    const timer = setTimeout(() => {
      fetchActivities();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Function to filter activities
    const filterActivities = async () => {
      setIsLoading(true);

      try {
        // Try to use the API for filtering if possible
        const token = localStorage.getItem('token');
        const useApi = token && activityLogger.useApi;

        if (useApi) {
          // Prepare API filter parameters
          const filters: any = {
            limit: 100 // Adjust as needed
          };

          // Map activity type to API filter
          if (activityType !== "All") {
            if (activityType === "Login") {
              // For login events, we'll need to filter client-side
              filters.search = "log";
            } else {
              filters.targetType = activityType.toUpperCase();
            }
          }

          // Add search term
          if (searchTerm) {
            filters.search = searchTerm;
          }

          // Add date range
          if (dateRange.start) {
            filters.startDate = dateRange.start;
          }

          if (dateRange.end) {
            filters.endDate = dateRange.end;
          }

          // Fetch filtered activities from API
          const apiActivities = await activityLogger.getActivities(filters);

          // For login events, we need additional client-side filtering
          let result = apiActivities;
          if (activityType === "Login") {
            result = result.filter((activity: Activity) =>
              activity.action.toLowerCase().includes("log")
            );
          }

          setFilteredActivities(result);
        } else {
          // Fallback to client-side filtering
          let result = activities;

          // Apply activity type filter
          if (activityType !== "All") {
            if (activityType === "Login") {
              result = result.filter(activity => activity.action.toLowerCase().includes("log"));
            } else {
              result = result.filter(activity => activity.targetType === activityType);
            }
          }

          // Apply search filter
          if (searchTerm) {
            const term = searchTerm.toLowerCase();
            result = result.filter(
              activity =>
                activity.user.toLowerCase().includes(term) ||
                activity.action.toLowerCase().includes(term) ||
                (activity.details && activity.details.toLowerCase().includes(term)) ||
                (activity.target && activity.target.toLowerCase().includes(term))
            );
          }

          // Apply date range filter
          if (dateRange.start) {
            const startDate = new Date(dateRange.start);
            result = result.filter(activity => new Date(activity.timestamp) >= startDate);
          }

          if (dateRange.end) {
            const endDate = new Date(dateRange.end);
            endDate.setHours(23, 59, 59, 999); // End of the day
            result = result.filter(activity => new Date(activity.timestamp) <= endDate);
          }

          setFilteredActivities(result);
        }
      } catch (error) {
        console.error('Error filtering activities:', error);
        // Fallback to client-side filtering on error
        let result = activities;

        // Apply activity type filter
        if (activityType !== "All") {
          if (activityType === "Login") {
            result = result.filter(activity => activity.action.toLowerCase().includes("log"));
          } else {
            result = result.filter(activity => activity.targetType === activityType);
          }
        }

        // Apply search filter
        if (searchTerm) {
          const term = searchTerm.toLowerCase();
          result = result.filter(
            activity =>
              activity.user.toLowerCase().includes(term) ||
              activity.action.toLowerCase().includes(term) ||
              (activity.details && activity.details.toLowerCase().includes(term)) ||
              (activity.target && activity.target.toLowerCase().includes(term))
          );
        }

        // Apply date range filter
        if (dateRange.start) {
          const startDate = new Date(dateRange.start);
          result = result.filter(activity => new Date(activity.timestamp) >= startDate);
        }

        if (dateRange.end) {
          const endDate = new Date(dateRange.end);
          endDate.setHours(23, 59, 59, 999); // End of the day
          result = result.filter(activity => new Date(activity.timestamp) <= endDate);
        }

        setFilteredActivities(result);
      } finally {
        setIsLoading(false);
      }
    };

    // Debounce the filter function to avoid too many API calls
    const debounceTimeout = setTimeout(() => {
      filterActivities();
    }, 300);

    return () => clearTimeout(debounceTimeout);
  }, [activityType, searchTerm, dateRange, activities]);

  // Column definitions for the DataTable
  const activityColumns = [
    {
      key: "timestamp",
      header: "Time",
      render: (value: string) => {
        const date = new Date(value);
        return (
          <div>
            <div>{date.toLocaleDateString()}</div>
            <div className="text-xs text-gray-500">{date.toLocaleTimeString()}</div>
          </div>
        );
      },
    },
    {
      key: "user",
      header: "User",
      render: (value: string, row: Activity) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-bold mr-2">
            {value.charAt(0)}
          </div>
          <div>
            <div>{value}</div>
            <div className="text-xs text-gray-500">{row.userRole}</div>
          </div>
        </div>
      ),
    },
    {
      key: "action",
      header: "Action",
      render: (value: string, row: Activity) => (
        <div>
          <span className="font-medium">{value}</span>
          {row.target && <span className="ml-1">for {row.target}</span>}
        </div>
      ),
    },
    {
      key: "details",
      header: "Details",
      render: (value: string | undefined) => value || "-",
    },
    {
      key: "targetType",
      header: "Category",
      render: (value: string | undefined) => (
        <span
          className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
            value === "User"
              ? "bg-blue-100 text-blue-800"
              : value === "Room"
              ? "bg-green-100 text-green-800"
              : value === "Payment"
              ? "bg-amber-100 text-amber-800"
              : value === "ServiceRequest"
              ? "bg-purple-100 text-purple-800"
              : value === "Utility"
              ? "bg-indigo-100 text-indigo-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {value || "System"}
        </span>
      ),
    },
    {
      key: "ipAddress",
      header: "IP Address",
      render: (value: string | undefined) => value || "-",
    },
  ];

  // Handler functions
  const handleViewActivity = (activity: Activity) => {
    setSelectedActivity(activity);
    setIsViewModalOpen(true);
  };

  const handleDateRangeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDateRange({
      ...dateRange,
      [name]: value,
    });
  };

  const clearFilters = () => {
    setActivityType("All");
    setSearchTerm("");
    setDateRange({ start: "", end: "" });
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Activity Log</h1>
          <p className="text-gray-600">Track all system activities and user actions</p>
        </div>
        <button
          onClick={clearFilters}
          className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center transition"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          Clear Filters
        </button>
      </div>

      {/* Filters and Search */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="col-span-1 md:col-span-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search activities..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
            <svg
              className="absolute right-3 top-2.5 h-5 w-5 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>
        <div>
          <input
            type="date"
            name="start"
            value={dateRange.start}
            onChange={handleDateRangeChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            placeholder="Start Date"
          />
        </div>
        <div>
          <input
            type="date"
            name="end"
            value={dateRange.end}
            onChange={handleDateRangeChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            placeholder="End Date"
          />
        </div>
      </div>

      {/* Activity Type Filters */}
      <div className="mb-6 flex flex-wrap gap-2">
        <button
          onClick={() => setActivityType("All")}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            activityType === "All"
              ? "bg-amber-500 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
        >
          All Activities
        </button>
        <button
          onClick={() => setActivityType("User")}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            activityType === "User"
              ? "bg-blue-500 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
        >
          User
        </button>
        <button
          onClick={() => setActivityType("Room")}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            activityType === "Room"
              ? "bg-green-500 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
        >
          Room
        </button>
        <button
          onClick={() => setActivityType("Payment")}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            activityType === "Payment"
              ? "bg-amber-500 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
        >
          Payment
        </button>
        <button
          onClick={() => setActivityType("ServiceRequest")}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            activityType === "ServiceRequest"
              ? "bg-purple-500 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
        >
          Service Request
        </button>
        <button
          onClick={() => setActivityType("Utility")}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            activityType === "Utility"
              ? "bg-indigo-500 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
        >
          Utility
        </button>
        <button
          onClick={() => setActivityType("System")}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            activityType === "System"
              ? "bg-gray-700 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
        >
          System
        </button>
        <button
          onClick={() => setActivityType("Login")}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            activityType === "Login"
              ? "bg-red-500 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
        >
          Login/Logout
        </button>
      </div>

      {/* Activities Table */}
      <DataTable
        columns={activityColumns}
        data={filteredActivities}
        emptyMessage="No activities found"
        isLoading={isLoading}
        onRowClick={handleViewActivity}
      />

      {/* View Activity Modal */}
      <Modal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        title="Activity Details"
        size="md"
      >
        {selectedActivity && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Timestamp</p>
                <p className="mt-1">
                  {new Date(selectedActivity.timestamp).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">IP Address</p>
                <p className="mt-1">{selectedActivity.ipAddress || "-"}</p>
              </div>
              <div className="col-span-2">
                <p className="text-sm font-medium text-gray-500">User</p>
                <div className="mt-1 flex items-center">
                  <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-bold mr-2">
                    {selectedActivity.user.charAt(0)}
                  </div>
                  <div>
                    <div>{selectedActivity.user}</div>
                    <div className="text-xs text-gray-500">{selectedActivity.userRole}</div>
                  </div>
                </div>
              </div>
              <div className="col-span-2">
                <p className="text-sm font-medium text-gray-500">Action</p>
                <p className="mt-1">
                  <span className="font-medium">{selectedActivity.action}</span>
                  {selectedActivity.target && (
                    <span className="ml-1">for {selectedActivity.target}</span>
                  )}
                </p>
              </div>
              <div className="col-span-2">
                <p className="text-sm font-medium text-gray-500">Category</p>
                <p className="mt-1">
                  <span
                    className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      selectedActivity.targetType === "User"
                        ? "bg-blue-100 text-blue-800"
                        : selectedActivity.targetType === "Room"
                        ? "bg-green-100 text-green-800"
                        : selectedActivity.targetType === "Payment"
                        ? "bg-amber-100 text-amber-800"
                        : selectedActivity.targetType === "ServiceRequest"
                        ? "bg-purple-100 text-purple-800"
                        : selectedActivity.targetType === "Utility"
                        ? "bg-indigo-100 text-indigo-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {selectedActivity.targetType || "System"}
                  </span>
                </p>
              </div>
            </div>

            {selectedActivity.details && (
              <div className="border-t border-gray-200 pt-4 mt-4">
                <p className="text-sm font-medium text-gray-500">Details</p>
                <p className="mt-1 text-sm text-gray-600">{selectedActivity.details}</p>
              </div>
            )}

            {selectedActivity.targetId && (
              <div className="border-t border-gray-200 pt-4 mt-4">
                <p className="text-sm font-medium text-gray-500">Technical Details</p>
                <div className="mt-1 text-sm text-gray-600">
                  <p>User ID: {selectedActivity.userId}</p>
                  {selectedActivity.targetId && (
                    <p>Target ID: {selectedActivity.targetId}</p>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
}
