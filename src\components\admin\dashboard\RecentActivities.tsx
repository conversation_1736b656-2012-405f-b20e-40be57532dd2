"use client";

import { motion } from 'framer-motion';
import Link from 'next/link';

interface Activity {
  id: number;
  user: string;
  action: string;
  amount?: string;
  details?: string;
  time: string;
  room: string;
}

interface RecentActivitiesProps {
  activities: Activity[];
  isLoading?: boolean;
}

export default function RecentActivities({ activities, isLoading = false }: RecentActivitiesProps) {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-semibold">Recent Activities</h2>
        <Link href="/cms/admin/activities" className="text-amber-600 text-sm hover:underline">
          View All
        </Link>
      </div>
      <div className="space-y-4">
        {isLoading ? (
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-start pb-4 border-b border-gray-100 last:border-0">
                <div className="w-10 h-10 rounded-full bg-gray-200 mr-3"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          activities.map((activity) => (
            <div key={activity.id} className="flex items-start pb-4 border-b border-gray-100 last:border-0">
              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-bold mr-3">
                {activity.user.charAt(0)}
              </div>
              <div className="flex-1">
                <p className="text-sm">
                  <span className="font-medium">{activity.user}</span> {activity.action}
                  {activity.amount && <span className="font-medium"> {activity.amount}</span>}
                  {activity.details && <span> {activity.details}</span>}
                </p>
                <div className="flex mt-1 text-xs text-gray-500">
                  <span>{activity.time}</span>
                  <span className="mx-2">•</span>
                  <span>Room {activity.room}</span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
