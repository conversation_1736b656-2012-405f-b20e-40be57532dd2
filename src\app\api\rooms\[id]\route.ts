import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET a specific room by ID
export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // Make sure to await params in Next.js App Router
    const { id } = context.params;

    const room = await prisma.room.findUnique({
      where: { id },
      include: {
        roomType: true,
        images: true,
        residents: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    if (!room) {
      return NextResponse.json(
        { success: false, message: 'Room not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error(`Error fetching room with ID ${context.params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch room' },
      { status: 500 }
    );
  }
}

// PUT update a room
async function PUT(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // Make sure to directly use params.id in Next.js App Router
    const { id } = context.params;
    const body = await request.json();
    const { roomNumber, roomTypeId, monthlyRate, isOccupied, description } = body;

    // Validate required fields
    if (!roomNumber) {
      return NextResponse.json(
        { success: false, message: 'Room number is required' },
        { status: 400 }
      );
    }

    // Check if room exists
    const existingRoom = await prisma.room.findUnique({
      where: { id }
    });

    if (!existingRoom) {
      return NextResponse.json(
        { success: false, message: 'Room not found' },
        { status: 404 }
      );
    }

    // Check if another room with the same number exists (excluding this one)
    if (roomNumber !== existingRoom.roomNumber) {
      const duplicateNumber = await prisma.room.findUnique({
        where: { roomNumber }
      });

      if (duplicateNumber) {
        return NextResponse.json(
          { success: false, message: 'Another room with this number already exists' },
          { status: 409 }
        );
      }
    }

    // If roomTypeId is provided, check if it exists
    if (roomTypeId) {
      const roomType = await prisma.roomType.findUnique({
        where: { id: roomTypeId }
      });

      if (!roomType) {
        return NextResponse.json(
          { success: false, message: 'Room type not found' },
          { status: 404 }
        );
      }
    }

    // Update the room
    const updatedRoom = await prisma.room.update({
      where: { id },
      data: {
        roomNumber,
        roomTypeId: roomTypeId || undefined,
        monthlyRate: monthlyRate !== undefined ? parseFloat(monthlyRate.toString()) : undefined,
        isOccupied: isOccupied !== undefined ? isOccupied : undefined,
        description: description !== undefined ? description : undefined
      },
      include: {
        roomType: true
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Room updated successfully',
      data: updatedRoom
    });
  } catch (error) {
    console.error(`Error updating room with ID ${context.params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to update room' },
      { status: 500 }
    );
  }
}

// DELETE a room
async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // Make sure to directly use params.id in Next.js App Router
    const { id } = context.params;

    // Check if room exists
    const existingRoom = await prisma.room.findUnique({
      where: { id },
      include: {
        residents: true
      }
    });

    if (!existingRoom) {
      return NextResponse.json(
        { success: false, message: 'Room not found' },
        { status: 404 }
      );
    }

    // Check if there are residents in this room
    if (existingRoom.residents.length > 0) {
      return NextResponse.json(
        {
          success: false,
          message: 'Cannot delete room with residents. Please reassign the residents first.'
        },
        { status: 400 }
      );
    }

    // Delete the room images first
    await prisma.roomImage.deleteMany({
      where: { roomId: id }
    });

    // Delete the room
    await prisma.room.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Room deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting room with ID ${context.params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete room' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware to PUT and DELETE methods
const protectedPUT = withAuth(PUT, ['ADMIN']);
const protectedDELETE = withAuth(DELETE, ['ADMIN']);

export { protectedPUT as PUT, protectedDELETE as DELETE };
