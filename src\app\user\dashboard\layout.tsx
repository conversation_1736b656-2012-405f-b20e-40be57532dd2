"use client";

import { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import DashboardSidebar from '@/components/dashboard/DashboardSidebar';

// export const metadata: Metadata = {
//   title: 'User Dashboard - Dormitory',
//   description: 'Manage your dormitory rental, payments, and utilities',
// };

interface UserData {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  room: {
    id: string;
    roomNumber: string;
    roomType: string;
  } | null;
}

export default function UserDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const { user: authUser } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user data from API
  useEffect(() => {
    const fetchUserData = async () => {
      if (!authUser) return;

      try {
        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication token not found. Please log in again.');
          setIsLoading(false);
          return;
        }

        const response = await fetch('/api/dashboard/overview', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch user data: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
          setUserData(result.data);
        } else {
          setError(result.message || 'Failed to fetch user data');
        }
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError('An error occurred while fetching user data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [authUser]);

  // Update active tab based on the current path
  useEffect(() => {
    const path = pathname.split('/').pop();
    if (path === 'dashboard') {
      setActiveTab('overview');
    } else if (path) {
      setActiveTab(path);
    }
  }, [pathname]);

  // Handle tab changes
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);

    // Navigate to the corresponding page
    if (tab === 'overview') {
      router.push('/user/dashboard');
    } else {
      router.push(`/user/dashboard/${tab}`);
    }
  };

  // Render content based on loading and data state
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex flex-col md:flex-row">
        {/* Sidebar */}
        <DashboardSidebar activeTab={activeTab} setActiveTab={handleTabChange} />

        {/* Main Content */}
        <main className="flex-1 p-4 md:p-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-gray-800">
                  {activeTab === 'overview' && 'Dashboard Overview'}
                  {activeTab === 'payments' && 'Payment History'}
                  {activeTab === 'utilities' && 'Utility Usage'}
                  {activeTab === 'service-requests' && 'Service Requests'}
                  {activeTab === 'settings' && 'Account Settings'}
                </h1>
                {isLoading ? (
                  <p className="text-gray-600">Loading user data...</p>
                ) : error ? (
                  <p className="text-red-600">{error}</p>
                ) : userData ? (
                  <p className="text-gray-600">Welcome back, {userData.user.name}</p>
                ) : (
                  <p className="text-gray-600">Welcome to your dashboard</p>
                )}
              </div>
              {userData && userData.room && (
                <div className="bg-white p-3 rounded-full shadow-sm">
                  <span className="font-medium text-amber-600">Room {userData.room.roomNumber}</span>
                </div>
              )}
            </div>

            {/* Page Content */}
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
