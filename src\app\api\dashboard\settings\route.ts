import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';
import bcrypt from 'bcrypt';

async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user exists
    if (!user || !user.id) {
      return NextResponse.json(
        { success: false, message: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get user data
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        address: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!userData) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Get user's notification preferences (this would be in a real app)
    // For now, we'll return mock data
    const notificationPreferences = {
      email: true,
      sms: true,
      app: true
    };

    // Get emergency contact (this would be in a real app)
    // For now, we'll return mock data
    const emergencyContact = {
      name: "Emergency Contact",
      relationship: "Family",
      phone: "************"
    };

    // Construct the response
    const settingsData = {
      user: userData,
      notifications: notificationPreferences,
      emergencyContact: emergencyContact,
      passwordLastChanged: userData.updatedAt // In a real app, you'd track password changes separately
    };

    return NextResponse.json({
      success: true,
      data: settingsData
    });
  } catch (error) {
    console.error('Error fetching settings data:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch settings data' },
      { status: 500 }
    );
  }
}

async function PUT(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user exists
    if (!user || !user.id) {
      return NextResponse.json(
        { success: false, message: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { 
      profile, 
      emergencyContact, 
      password, 
      notifications 
    } = body;

    // Update user profile if provided
    if (profile) {
      const { name, email, phone, address } = profile;
      
      // Check if email is already taken by another user
      if (email && email !== user.email) {
        const existingUser = await prisma.user.findUnique({
          where: { email }
        });
        
        if (existingUser && existingUser.id !== user.id) {
          return NextResponse.json(
            { success: false, message: 'Email is already in use' },
            { status: 400 }
          );
        }
      }
      
      // Update user profile
      await prisma.user.update({
        where: { id: user.id },
        data: {
          name: name || undefined,
          email: email || undefined,
          phone: phone || undefined,
          address: address || undefined
        }
      });
    }

    // Update password if provided
    if (password) {
      const { currentPassword, newPassword } = password;
      
      // Verify current password
      const userData = await prisma.user.findUnique({
        where: { id: user.id }
      });
      
      if (!userData) {
        return NextResponse.json(
          { success: false, message: 'User not found' },
          { status: 404 }
        );
      }
      
      const isPasswordValid = await bcrypt.compare(currentPassword, userData.password);
      
      if (!isPasswordValid) {
        return NextResponse.json(
          { success: false, message: 'Current password is incorrect' },
          { status: 400 }
        );
      }
      
      // Hash the new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      
      // Update the password
      await prisma.user.update({
        where: { id: user.id },
        data: {
          password: hashedPassword
        }
      });
    }

    // In a real app, you would update emergency contact and notification preferences
    // For now, we'll just acknowledge the request

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully'
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update settings' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedGET = withAuth(GET, ['ADMIN', 'RESIDENT', 'VISITORS']);
const protectedPUT = withAuth(PUT, ['ADMIN', 'RESIDENT', 'VISITORS']);

export { protectedGET as GET, protectedPUT as PUT };
