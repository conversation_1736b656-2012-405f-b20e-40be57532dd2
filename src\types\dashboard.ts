import { PaymentStatus, PaymentType, ServiceRequestStatus, ServiceRequestType, Priority, UserRole } from '@prisma/client';

export interface DashboardUser {
  id: string;
  name: string;
  email: string;
  role: UserRole;
}

export interface DashboardRoom {
  id: string;
  roomNumber: string;
  roomType: string;
}

export interface DashboardContract {
  id: string;
  startDate: Date;
  endDate: Date;
  rentAmount: number;
  depositAmount: number;
}

export interface DashboardPayment {
  id: string;
  amount: number;
  date: Date;
  dueDate: Date;
  paidDate: Date | null;
  status: PaymentStatus;
  type: PaymentType;
}

export interface DashboardServiceRequest {
  id: string;
  type: ServiceRequestType;
  title: string;
  description: string;
  status: ServiceRequestStatus;
  priority: Priority;
  createdAt: Date;
  updatedAt: Date;
  adminResponse: string | null;
}

export interface DashboardUtilityReading {
  current: number;
  previous: number;
  usage: number;
  rate: number;
}

export interface DashboardUtilities {
  water: DashboardUtilityReading;
  electricity: DashboardUtilityReading;
}

export interface DashboardData {
  user: DashboardUser;
  room: DashboardRoom | null;
  contract: DashboardContract | null;
  nextPayment: DashboardPayment | null;
  payments: DashboardPayment[];
  serviceRequests: DashboardServiceRequest[];
  utilities: DashboardUtilities | null;
}

export interface DashboardResponse {
  success: boolean;
  data: DashboardData;
  message?: string;
}
