// Payment scheduler service for handling recurring payments

/**
 * Generate monthly payments for a specific month and year
 * @param month Month number (1-12)
 * @param year Year (e.g., 2023)
 * @param paymentType Type of payment (default: RENT)
 * @returns Promise with the API response
 */
export async function generateMonthlyPayments(
  month: number,
  year: number,
  paymentType: string = 'RENT'
) {
  try {
    const response = await fetch('/api/admin/payments/generate-monthly', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        month,
        year,
        paymentType
      })
    });

    return response.json();
  } catch (error) {
    console.error('Error generating monthly payments:', error);
    throw error;
  }
}

/**
 * Send payment reminders for upcoming or overdue payments
 * @param paymentIds Array of payment IDs to send reminders for
 * @returns Promise with the API response
 */
export async function sendPaymentReminders(paymentIds: string[]) {
  try {
    const response = await fetch('/api/admin/payments/send-reminders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        paymentIds
      })
    });

    return response.json();
  } catch (error) {
    console.error('Error sending payment reminders:', error);
    throw error;
  }
}

/**
 * Get all payments with optional filtering
 * @param filters Optional filters for payments
 * @returns Promise with the API response
 */
export async function getPayments(filters: {
  status?: string;
  type?: string;
  userId?: string;
  roomId?: string;
  startDate?: string;
  endDate?: string;
  month?: number;
  year?: number;
} = {}) {
  try {
    // Build query string from filters
    const queryParams = new URLSearchParams();

    if (filters.status) queryParams.append('status', filters.status);
    if (filters.type) queryParams.append('type', filters.type);
    if (filters.userId) queryParams.append('userId', filters.userId);
    if (filters.roomId) queryParams.append('roomId', filters.roomId);
    if (filters.startDate) queryParams.append('startDate', filters.startDate);
    if (filters.endDate) queryParams.append('endDate', filters.endDate);

    // Add month and year filters if provided
    if (filters.month !== undefined) queryParams.append('month', filters.month.toString());
    if (filters.year !== undefined) queryParams.append('year', filters.year.toString());

    const queryString = queryParams.toString();
    const url = `/api/admin/payments${queryString ? `?${queryString}` : ''}`;

    // Get token from localStorage
    const token = localStorage.getItem('token');

    if (!token) {
      console.error('No authentication token found in localStorage');
      return {
        success: false,
        message: 'Authentication token not found. Please log in again.'
      };
    }

    console.log('Fetching payments from:', url);

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      console.error('Error response from server:', response.status, response.statusText);
      return {
        success: false,
        message: `Server returned error: ${response.status} ${response.statusText}`
      };
    }

    const data = await response.json();
    console.log('Payments data received:', data);
    return data;
  } catch (error) {
    console.error('Error fetching payments:', error);
    return {
      success: false,
      message: `Failed to fetch payments: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Create a new payment
 * @param paymentData Payment data
 * @returns Promise with the API response
 */
export async function createPayment(paymentData: {
  userId: string;
  roomId: string;
  amount: number;
  dueDate: string;
  status: string;
  type: string;
  notes?: string;
  paidDate?: string;
  paymentMethod?: string;
  receiptNumber?: string;
}) {
  try {
    const response = await fetch('/api/admin/payments', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(paymentData)
    });

    return response.json();
  } catch (error) {
    console.error('Error creating payment:', error);
    throw error;
  }
}

/**
 * Update a payment
 * @param id Payment ID
 * @param paymentData Payment data to update
 * @returns Promise with the API response
 */
export async function updatePayment(
  id: string,
  paymentData: {
    amount?: number;
    dueDate?: string;
    paidDate?: string;
    status?: string;
    notes?: string;
    paymentMethod?: string;
    receiptNumber?: string;
  }
) {
  try {
    const response = await fetch(`/api/admin/payments/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(paymentData)
    });

    return response.json();
  } catch (error) {
    console.error('Error updating payment:', error);
    throw error;
  }
}

/**
 * Delete a payment
 * @param id Payment ID
 * @returns Promise with the API response
 */
export async function deletePayment(id: string) {
  try {
    const response = await fetch(`/api/admin/payments/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    return response.json();
  } catch (error) {
    console.error('Error deleting payment:', error);
    throw error;
  }
}

/**
 * Mark a payment as paid
 * @param id Payment ID
 * @param paymentMethod Payment method used
 * @param paidDate Date of payment (defaults to current date)
 * @param notes Optional notes about the payment
 * @returns Promise with the API response
 */
export async function markPaymentAsPaid(
  id: string,
  paymentMethod: string,
  paidDate?: string,
  notes?: string
) {
  try {
    const response = await fetch(`/api/admin/payments/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        status: 'PAID',
        paidDate: paidDate || new Date().toISOString(),
        paymentMethod,
        notes: notes || undefined,
        receiptNumber: `RCT-${new Date().getFullYear()}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`
      })
    });

    return response.json();
  } catch (error) {
    console.error('Error marking payment as paid:', error);
    throw error;
  }
}

/**
 * Get payment statistics
 * @returns Promise with payment statistics
 */
export async function getPaymentStatistics() {
  try {
    const response = await fetch('/api/admin/payments/statistics', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    return response.json();
  } catch (error) {
    console.error('Error fetching payment statistics:', error);
    throw error;
  }
}
