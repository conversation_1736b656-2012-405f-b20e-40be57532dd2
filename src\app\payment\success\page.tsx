"use client";

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';

export default function PaymentSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, refreshUserData } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    // Refresh user data to update role
    const updateUserData = async () => {
      try {
        await refreshUserData();
        setIsLoading(false);
      } catch (error) {
        console.error('Error refreshing user data:', error);
        setIsLoading(false);
      }
    };

    if (sessionId) {
      updateUserData();
    } else {
      setIsLoading(false);
    }

    // Only run this effect once when the component mounts
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-8 text-center">
        <div className="mb-8">
          <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        </div>

        <h1 className="text-3xl font-bold mb-4 text-green-600">Payment Successful!</h1>

        <p className="text-xl mb-6">
          Thank you for your payment. Your room booking has been confirmed.
        </p>

        <div className="mb-8 p-4 bg-gray-50 rounded-lg inline-block">
          <p className="text-gray-700">
            Transaction ID: <span className="font-medium">{sessionId}</span>
          </p>
        </div>

        <p className="mb-8 text-gray-600">
          A confirmation email has been sent to your registered email address.
          You can view your booking details and payment history in your dashboard.
        </p>

        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link href="/user/dashboard" className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition">
            Go to Dashboard
          </Link>
          <Link href="/" className="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg font-medium hover:bg-gray-300 transition">
            Return to Home
          </Link>
        </div>
      </div>
    </div>
  );
}
