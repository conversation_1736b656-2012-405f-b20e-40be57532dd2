import { NextRequest, NextResponse } from 'next/server';
import { verifyJwtToken, UserPayload } from '@/lib/jwt';

type Handler = (
  req: NextRequest,
  params?: any
) => Promise<NextResponse> | NextResponse;

export function withAuth(handler: Handler, allowedRoles?: string[]) {
  return async (req: NextRequest, params?: any) => {
    try {
      // Get the authorization header
      const authHeader = req.headers.get('authorization');

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { success: false, message: 'Unauthorized: Missing or invalid token' },
          { status: 401 }
        );
      }

      // Extract the token
      const token = authHeader.split(' ')[1];

      // Verify the token
      const decoded = verifyJwtToken(token) as UserPayload | null;

      if (!decoded) {
        return NextResponse.json(
          { success: false, message: 'Unauthorized: Invalid token' },
          { status: 401 }
        );
      }

      // Check if user has the required role
      if (allowedRoles && allowedRoles.length > 0) {
        const userRole = decoded.role;

        if (!userRole || !allowedRoles.includes(userRole)) {
          return NextResponse.json(
            { success: false, message: 'Forbidden: Insufficient permissions' },
            { status: 403 }
          );
        }
      }

      // Add the user to the request object
      (req as any).user = decoded;

      // Call the original handler
      return handler(req, params);
    } catch (error) {
      console.error('Authentication error:', error);
      return NextResponse.json(
        { success: false, message: 'Authentication error' },
        { status: 401 }
      );
    }
  };
}
