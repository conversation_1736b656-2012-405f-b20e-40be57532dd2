"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import DataTable from "@/components/admin/DataTable";
import Modal from "@/components/admin/Modal";
import FormField from "@/components/admin/FormField";
import activityLogger from "@/services/activityLogger";
import {
  getPayments,
  createPayment,
  updatePayment,
  markPaymentAsPaid,
  generateMonthlyPayments,
  deletePayment
} from "@/services/paymentSchedulerService";
import { createPaymentReceivedNotification } from "@/services/notificationService";
import { toast } from "react-hot-toast";
import Swal from "sweetalert2";

// Payment interface
interface Payment {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  room: {
    id: string;
    roomNumber: string;
    roomType: string;
  };
  amount: number;
  dueDate: string;
  paidDate?: string;
  status: "PAID" | "PENDING" | "UPCOMING" | "OVERDUE";
  type: "RENT" | "UTILITIES" | "DEPOSIT" | "MAINTENANCE" | "OTHER";
  notes?: string;
  receiptNumber?: string;
  receiptUrl?: string;
  paymentMethod?: string;
  createdAt: string;
  updatedAt: string;
}

export default function PaymentManagement() {
  const [isLoading, setIsLoading] = useState(true);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [filteredPayments, setFilteredPayments] = useState<Payment[]>([]);
  const [filter, setFilter] = useState<"All" | "PENDING" | "PAID" | "UPCOMING" | "OVERDUE">("All");
  const [searchTerm, setSearchTerm] = useState("");

  // Date filter state
  const currentDate = new Date();
  const [dateFilter, setDateFilter] = useState({
    month: currentDate.getMonth(), // 0-11
    year: currentDate.getFullYear(),
  });
  const [isDateFilterActive, setIsDateFilterActive] = useState(false);

  // Modal states
  const [isRecordModalOpen, setIsRecordModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isGenerateMonthlyModalOpen, setIsGenerateMonthlyModalOpen] = useState(false);
  const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

  // New payment form state
  const [newPayment, setNewPayment] = useState({
    userRoomId: "", // Combined userId:roomId
    amount: 0,
    dueDate: new Date().toISOString().split("T")[0],
    status: "PENDING" as "PENDING" | "PAID" | "UPCOMING" | "OVERDUE",
    type: "RENT" as "RENT" | "UTILITIES" | "DEPOSIT" | "MAINTENANCE" | "OTHER",
    notes: "",
  });

  // Generate monthly payments form state
  const [monthlyPaymentForm, setMonthlyPaymentForm] = useState({
    month: new Date().getMonth() + 1, // Current month (1-12)
    year: new Date().getFullYear(),
    paymentType: "RENT" as const,
  });

  // Users and rooms state
  const [combinedUserRooms, setCombinedUserRooms] = useState<{ value: string; label: string; userId: string; roomId: string }[]>([]);

  // Payment types for dropdown
  const paymentTypes = [
    { value: "RENT", label: "Rent" },
    { value: "UTILITIES", label: "Utilities" },
    { value: "DEPOSIT", label: "Deposit" },
    { value: "MAINTENANCE", label: "Maintenance" },
    { value: "OTHER", label: "Other" },
  ];

  // Payment statuses for dropdown
  const paymentStatuses = [
    { value: "PENDING", label: "Pending" },
    { value: "PAID", label: "Paid" },
    { value: "UPCOMING", label: "Upcoming" },
    { value: "OVERDUE", label: "Overdue" },
  ];

  // Check if user is authenticated
  const checkAuthentication = () => {
    const token = localStorage.getItem('token');
    if (!token) {
      toast.error("You are not authenticated. Please log in.");
      return false;
    }
    return true;
  };

  // Fetch payments from API
  const fetchPayments = async (useFilters = false) => {
    setIsLoading(true);

    if (!checkAuthentication()) {
      setIsLoading(false);
      return;
    }

    try {
      console.log("Fetching payments data...");

      // Apply month/year filter if active and requested
      const filters: any = {};
      if (useFilters && isDateFilterActive) {
        filters.month = dateFilter.month;
        filters.year = dateFilter.year;
        console.log(`Applying date filter: ${getMonthName(dateFilter.month)} ${dateFilter.year}`);
      }

      const response = await getPayments(filters);

      if (response.success) {
        console.log("Payments data received:", response.data);
        setPayments(response.data || []);
        setFilteredPayments(response.data || []);

        if (response.data && response.data.length === 0) {
          if (isDateFilterActive && useFilters) {
            toast(`No payment records found for ${getMonthName(dateFilter.month)} ${dateFilter.year}.`);
          } else {
            toast("No payment records found in the database.");
          }
        }
      } else {
        console.error("Failed to fetch payments:", response.message);
        toast.error(response.message || "Failed to fetch payments");

        // If unauthorized, try to refresh the page or redirect to login
        if (response.message?.includes('Unauthorized') || response.message?.includes('token')) {
          toast.error("Authentication error. Please log in again.");
        }
      }
    } catch (error) {
      console.error("Error fetching payments:", error);
      toast.error("An error occurred while fetching payments");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch users and rooms for dropdowns
  const fetchUsersAndRooms = async () => {
    if (!checkAuthentication()) {
      return;
    }

    try {
      console.log("Fetching users and rooms data...");

      // Fetch users
      const token = localStorage.getItem('token');
      const usersResponse = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!usersResponse.ok) {
        console.error("Error fetching users:", usersResponse.status, usersResponse.statusText);
        toast.error(`Failed to fetch users: ${usersResponse.status} ${usersResponse.statusText}`);
        return;
      }

      const usersData = await usersResponse.json();

      if (usersData.success) {
        console.log("Users data received:", usersData.data);

        // Create combined user-room options
        const combinedOptions: { value: string; label: string; userId: string; roomId: string }[] = [];

        usersData.data.forEach((user: any) => {
          if (user.rooms && user.rooms.length > 0) {
            user.rooms.forEach((room: any) => {
              combinedOptions.push({
                value: `${user.id}:${room.id}`,
                label: `${user.email} - ${room.roomNumber}`,
                userId: user.id,
                roomId: room.id
              });
            });
          }
        });

        setCombinedUserRooms(combinedOptions);
      } else {
        toast.error(usersData.message || "Failed to fetch users");
      }
    } catch (error) {
      console.error("Error fetching users and rooms:", error);
      toast.error("An error occurred while fetching users and rooms");
    }
  };

  useEffect(() => {
    // Load data when component mounts
    console.log("Payment management component mounted");
    fetchPayments();
    fetchUsersAndRooms();
  }, []);

  // Function to handle date filter changes
  const handleDateFilterChange = (month: number, year: number) => {
    setDateFilter({ month, year });
    setIsDateFilterActive(true);

    // Fetch payments with the new date filter
    fetchPayments(true);
  };

  // Function to clear date filter
  const clearDateFilter = () => {
    setIsDateFilterActive(false);

    // Fetch all payments without date filter
    fetchPayments(false);
  };

  // Function to get month name
  const getMonthName = (month: number) => {
    return new Date(0, month).toLocaleString('default', { month: 'long' });
  };

  // Function to group payments by month
  const groupPaymentsByMonth = (payments: Payment[]) => {
    const grouped: Record<string, Payment[]> = {};

    payments.forEach(payment => {
      const date = new Date(payment.dueDate);
      const key = `${date.getFullYear()}-${date.getMonth()}`;

      if (!grouped[key]) {
        grouped[key] = [];
      }

      grouped[key].push(payment);
    });

    // Convert to array and sort by date (newest first)
    return Object.entries(grouped)
      .map(([key, payments]) => {
        const [year, month] = key.split('-').map(Number);
        return {
          key,
          displayName: `${getMonthName(month)} ${year}`,
          payments,
          month,
          year
        };
      })
      .sort((a, b) => {
        // Sort by year and month (descending)
        if (a.year !== b.year) return b.year - a.year;
        return b.month - a.month;
      });
  };

  useEffect(() => {
    // Filter payments based on filter, search term, and date
    let result = [...payments];

    // Apply status filter
    if (filter !== "All") {
      result = result.filter(payment => payment.status === filter);
    }

    // Apply date filter
    if (isDateFilterActive) {
      result = result.filter(payment => {
        const paymentDate = new Date(payment.dueDate);
        return (
          paymentDate.getMonth() === dateFilter.month &&
          paymentDate.getFullYear() === dateFilter.year
        );
      });
    }

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(
        payment =>
          payment.user.name.toLowerCase().includes(term) ||
          payment.room.roomNumber.toLowerCase().includes(term) ||
          payment.type.toLowerCase().includes(term)
      );
    }

    setFilteredPayments(result);
  }, [filter, searchTerm, payments, isDateFilterActive, dateFilter]);

  // Column definitions for the DataTable
  const paymentColumns = [
    {
      key: "user",
      header: "Resident - Room",
      render: (value: any, row: any) =>
        value && row.room ? `${value.email} - ${row.room.roomNumber}` : "-"
    },
    {
      key: "type",
      header: "Type",
      render: (value: string) => {
        const typeMap: Record<string, string> = {
          "RENT": "Rent",
          "UTILITIES": "Utilities",
          "DEPOSIT": "Deposit",
          "MAINTENANCE": "Maintenance",
          "OTHER": "Other"
        };
        return typeMap[value] || value;
      }
    },
    {
      key: "amount",
      header: "Amount",
      render: (value: number) => `฿${value.toLocaleString()}`,
    },
    {
      key: "dueDate",
      header: "Due Date",
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: "paidDate",
      header: "Paid Date",
      render: (value: string | undefined) =>
        value ? new Date(value).toLocaleDateString() : "-",
    },
    {
      key: "status",
      header: "Status",
      render: (value: string) => (
        <span
          className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
            value === "PAID"
              ? "bg-green-100 text-green-800"
              : value === "PENDING"
              ? "bg-amber-100 text-amber-800"
              : value === "OVERDUE"
              ? "bg-red-100 text-red-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {value === "PAID" ? "Paid" :
           value === "PENDING" ? "Pending" :
           value === "UPCOMING" ? "Upcoming" :
           value === "OVERDUE" ? "Overdue" : value}
        </span>
      ),
    },
    {
      key: "actions",
      header: "Actions",
      render: (_: any, row: Payment) => (
        <div className="flex space-x-2 justify-end">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleViewPayment(row);
            }}
            className="text-blue-600 hover:text-blue-800"
          >
            View
          </button>
          {row.status === "PENDING" && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleMarkAsPaid(row);
              }}
              className="text-green-600 hover:text-green-800"
            >
              Mark Paid
            </button>
          )}
          {row.status === "PAID" && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleGenerateReceipt(row);
              }}
              className="text-gray-600 hover:text-gray-800"
            >
              Receipt
            </button>
          )}
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleDeletePayment(row);
            }}
            className="text-red-600 hover:text-red-800"
          >
            Delete
          </button>
        </div>
      ),
    },
  ];

  // Handler functions
  const handleViewPayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsViewModalOpen(true);
  };

  const handleMarkAsPaid = async (payment: Payment) => {
    try {
      const response = await markPaymentAsPaid(
        payment.id,
        "Manual Payment",
        new Date().toISOString(),
        "Marked as paid by administrator"
      );

      if (response.success) {
        // Show SweetAlert notification
        Swal.fire({
          title: 'Success!',
          text: 'Payment has been marked as paid',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#f59e0b', // amber-500 color
        });

        fetchPayments(); // Refresh the payments list

        // Log the activity
        activityLogger.logPaymentRecord(
          "admin1", // In a real app, this would be the current user's ID
          "Admin User", // In a real app, this would be the current user's name
          "Admin", // In a real app, this would be the current user's role
          payment.user.id,
          payment.user.name,
          payment.amount,
          payment.type
        );
      } else {
        toast.error(response.message || "Failed to mark payment as paid");
      }
    } catch (error) {
      console.error("Error marking payment as paid:", error);
      toast.error("An error occurred while marking payment as paid");
    }
  };

  const handleGenerateReceipt = (payment: Payment) => {
    // Generate and download a receipt
    if (!payment.receiptNumber) {
      toast.error("No receipt number available for this payment");
      return;
    }

    // In a real app, this would call an API to generate a PDF receipt
    toast.success(`Generating receipt ${payment.receiptNumber}`);

    // Simulate downloading a receipt
    setTimeout(() => {
      const element = document.createElement("a");
      element.setAttribute("href", "data:text/plain;charset=utf-8," + encodeURIComponent(`
        RECEIPT
        --------
        Receipt Number: ${payment.receiptNumber}
        Date: ${new Date(payment.paidDate || "").toLocaleDateString()}
        Resident: ${payment.user.name}
        Room: ${payment.room.roomNumber}
        Amount: ฿${payment.amount.toLocaleString()}
        Payment Type: ${payment.type}
        Payment Method: ${payment.paymentMethod || "N/A"}
        --------
        Thank you for your payment!
      `));
      element.setAttribute("download", `Receipt-${payment.receiptNumber}.txt`);
      element.style.display = "none";
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);
    }, 1000);
  };

  const handleDeletePayment = (payment: Payment) => {
    // Set the selected payment
    setSelectedPayment(payment);

    // Show SweetAlert confirmation dialog
    Swal.fire({
      title: 'Are you sure?',
      text: `Delete ${payment.type.toLowerCase()} payment of ฿${payment.amount.toLocaleString()} for ${payment.user.name}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#f59e0b', // amber-500 color
      cancelButtonColor: '#6b7280', // gray-500 color
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        // If confirmed, open the confirmation modal
        setIsDeleteConfirmModalOpen(true);
      }
    });
  };

  const confirmDeletePayment = async () => {
    if (!selectedPayment) return;

    try {
      const response = await deletePayment(selectedPayment.id);

      if (response.success) {
        // Close the modal
        setIsDeleteConfirmModalOpen(false);

        // Show SweetAlert notification
        Swal.fire({
          title: 'Deleted!',
          text: 'Payment has been deleted successfully',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#f59e0b', // amber-500 color
        });

        // Log the activity
        activityLogger.logActivity({
          userId: "admin1", // In a real app, this would be the current user's ID
          user: "Admin User", // In a real app, this would be the current user's name
          userRole: "Admin", // In a real app, this would be the current user's role
          action: "deleted payment",
          details: `Deleted ${selectedPayment.type.toLowerCase()} payment of ฿${selectedPayment.amount.toLocaleString()} for ${selectedPayment.user.name}`,
          target: selectedPayment.user.name,
          targetId: selectedPayment.user.id,
          targetType: "Payment"
        });

        // Refresh the payments list
        fetchPayments();
      } else {
        toast.error(response.message || "Failed to delete payment");
      }
    } catch (error) {
      console.error("Error deleting payment:", error);
      toast.error("An error occurred while deleting payment");
    }
  };

  const handleRecordPayment = async () => {
    try {
      // Validate form
      if (!newPayment.userRoomId || !newPayment.amount || !newPayment.dueDate) {
        toast.error("Please fill in all required fields");
        return;
      }

      // Extract userId and roomId from the combined field
      const [userId, roomId] = newPayment.userRoomId.split(':');

      if (!userId || !roomId) {
        toast.error("Invalid resident/room selection");
        return;
      }

      // Create payment via API
      const response = await createPayment({
        userId: userId,
        roomId: roomId,
        amount: newPayment.amount,
        dueDate: newPayment.dueDate,
        status: newPayment.status,
        type: newPayment.type,
        notes: newPayment.notes,
        paidDate: newPayment.status === "PAID" ? new Date().toISOString() : undefined,
        paymentMethod: newPayment.status === "PAID" ? "Manual Entry" : undefined,
        receiptNumber: newPayment.status === "PAID" ?
          `RCT-${new Date().getFullYear()}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}` :
          undefined
      });

      if (response.success) {
        // Close the modal and reset form
        setIsRecordModalOpen(false);

        // Reset form
        setNewPayment({
          userRoomId: "",
          amount: 0,
          dueDate: new Date().toISOString().split("T")[0],
          status: "PENDING" as "PENDING" | "PAID" | "UPCOMING" | "OVERDUE",
          type: "RENT" as "RENT" | "UTILITIES" | "DEPOSIT" | "MAINTENANCE" | "OTHER",
          notes: "",
        });

        // Show SweetAlert notification
        Swal.fire({
          title: 'Success!',
          text: 'Payment has been recorded successfully',
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#f59e0b', // amber-500 color
        });

        // Refresh the payments list
        fetchPayments();
      } else {
        toast.error(response.message || "Failed to record payment");
      }
    } catch (error) {
      console.error("Error recording payment:", error);
      toast.error("An error occurred while recording payment");
    }
  };

  const handleGenerateMonthlyPayments = async () => {
    try {
      setIsLoading(true);
      const response = await generateMonthlyPayments(
        monthlyPaymentForm.month,
        monthlyPaymentForm.year,
        monthlyPaymentForm.paymentType
      );

      if (response.success) {
        // Close the modal
        setIsGenerateMonthlyModalOpen(false);

        // Show SweetAlert notification
        Swal.fire({
          title: 'Success!',
          text: `Generated ${response.data.totalCreated} payments for ${new Date(monthlyPaymentForm.year, monthlyPaymentForm.month - 1).toLocaleString('default', { month: 'long', year: 'numeric' })}`,
          icon: 'success',
          confirmButtonText: 'OK',
          confirmButtonColor: '#f59e0b', // amber-500 color
        });

        // Refresh the payments list
        fetchPayments();
      } else {
        toast.error(response.message || "Failed to generate monthly payments");
      }
    } catch (error) {
      console.error("Error generating monthly payments:", error);
      toast.error("An error occurred while generating monthly payments");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewPayment({
      ...newPayment,
      [name]: name === "amount" ? parseFloat(value) || 0 : value,
    });
  };

  const handleMonthlyPaymentInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setMonthlyPaymentForm({
      ...monthlyPaymentForm,
      [name]: name === "month" || name === "year" ? parseInt(value) : value,
    });
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Payment Management</h1>
          <p className="text-gray-600">Manage and track all dormitory payments</p>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => fetchPayments(false)}
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center transition"
            disabled={isLoading}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-5 w-5 mr-2 ${isLoading ? 'animate-spin' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            {isLoading ? 'Refreshing...' : 'Refresh'}
          </button>
          <button
            onClick={() => setIsGenerateMonthlyModalOpen(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center transition"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            Generate Monthly
          </button>
          <button
            onClick={() => {
              // Reset form before opening modal
              setNewPayment({
                userRoomId: "",
                amount: 0,
                dueDate: new Date().toISOString().split("T")[0],
                status: "PENDING" as "PENDING" | "PAID" | "UPCOMING" | "OVERDUE",
                type: "RENT" as "RENT" | "UTILITIES" | "DEPOSIT" | "MAINTENANCE" | "OTHER",
                notes: "",
              });
              setIsRecordModalOpen(true);
            }}
            className="bg-amber-500 hover:bg-amber-600 text-white px-4 py-2 rounded-lg flex items-center transition"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Record Payment
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="mb-6 flex flex-col space-y-4">
        {/* Status Filters */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setFilter("All")}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              filter === "All"
                ? "bg-amber-500 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter("PENDING")}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              filter === "PENDING"
                ? "bg-amber-500 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Pending
          </button>
          <button
            onClick={() => setFilter("PAID")}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              filter === "PAID"
                ? "bg-amber-500 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Paid
          </button>
          <button
            onClick={() => setFilter("UPCOMING")}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              filter === "UPCOMING"
                ? "bg-amber-500 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Upcoming
          </button>
          <button
            onClick={() => setFilter("OVERDUE")}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              filter === "OVERDUE"
                ? "bg-red-500 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Overdue
          </button>
        </div>

        {/* Month Filter and Search */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-2 bg-gray-100 p-2 rounded-lg">
              <select
                value={dateFilter.month}
                onChange={(e) => handleDateFilterChange(parseInt(e.target.value), dateFilter.year)}
                className="bg-transparent border-none focus:ring-0 text-sm font-medium text-gray-700"
              >
                {Array.from({ length: 12 }, (_, i) => (
                  <option key={i} value={i}>
                    {getMonthName(i)}
                  </option>
                ))}
              </select>
              <select
                value={dateFilter.year}
                onChange={(e) => handleDateFilterChange(dateFilter.month, parseInt(e.target.value))}
                className="bg-transparent border-none focus:ring-0 text-sm font-medium text-gray-700"
              >
                {Array.from({ length: 5 }, (_, i) => (
                  <option key={i} value={currentDate.getFullYear() - 2 + i}>
                    {currentDate.getFullYear() - 2 + i}
                  </option>
                ))}
              </select>
              {isDateFilterActive && (
                <button
                  onClick={clearDateFilter}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
            {isDateFilterActive && (
              <span className="text-sm text-amber-600 font-medium">
                Showing payments for {getMonthName(dateFilter.month)} {dateFilter.year}
              </span>
            )}
          </div>
          <div className="relative w-full sm:w-64">
            <input
              type="text"
              placeholder="Search payments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
            <svg
              className="absolute right-3 top-2.5 h-5 w-5 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Payments Table */}
      {isLoading ? (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
        </div>
      ) : payments.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-16 w-16 mx-auto text-gray-400 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Payment Records Found</h3>
          <p className="text-gray-600 mb-6">
            There are no payment records in the database yet. You can create a new payment or generate monthly payments.
          </p>
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => {
                // Reset form before opening modal
                setNewPayment({
                  userRoomId: "",
                  amount: 0,
                  dueDate: new Date().toISOString().split("T")[0],
                  status: "PENDING" as "PENDING" | "PAID" | "UPCOMING" | "OVERDUE",
                  type: "RENT" as "RENT" | "UTILITIES" | "DEPOSIT" | "MAINTENANCE" | "OTHER",
                  notes: "",
                });
                setIsRecordModalOpen(true);
              }}
              className="bg-amber-500 hover:bg-amber-600 text-white px-4 py-2 rounded-lg transition"
            >
              Record Payment
            </button>
            <button
              onClick={() => setIsGenerateMonthlyModalOpen(true)}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition"
            >
              Generate Monthly
            </button>
          </div>
        </div>
      ) : filteredPayments.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 mx-auto text-gray-400 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
            />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Payments Match Your Filters</h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your filters or search criteria to see more results.
          </p>
          {isDateFilterActive && (
            <button
              onClick={clearDateFilter}
              className="text-amber-600 hover:text-amber-800 font-medium"
            >
              Clear Date Filter
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-8">
          {isDateFilterActive ? (
            // When date filter is active, show a single month
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="bg-amber-50 px-6 py-4 border-b border-amber-100">
                <h3 className="text-lg font-medium text-amber-800">
                  {getMonthName(dateFilter.month)} {dateFilter.year}
                </h3>
              </div>
              <DataTable
                columns={paymentColumns}
                data={filteredPayments}
                emptyMessage="No payments found for this month"
                isLoading={false}
                onRowClick={handleViewPayment}
              />
            </div>
          ) : (
            // When no date filter, group by month
            groupPaymentsByMonth(filteredPayments).map((monthGroup) => (
              <div key={monthGroup.key} className="bg-white rounded-lg shadow overflow-hidden">
                <div className="bg-amber-50 px-6 py-4 border-b border-amber-100 flex justify-between items-center">
                  <h3 className="text-lg font-medium text-amber-800">
                    {monthGroup.displayName}
                  </h3>
                  <button
                    onClick={() => handleDateFilterChange(monthGroup.month, monthGroup.year)}
                    className="text-sm text-amber-600 hover:text-amber-800 font-medium"
                  >
                    Filter to this month
                  </button>
                </div>
                <DataTable
                  columns={paymentColumns}
                  data={monthGroup.payments}
                  emptyMessage="No payments found for this month"
                  isLoading={false}
                  onRowClick={handleViewPayment}
                />
              </div>
            ))
          )}
        </div>
      )}

      {/* Record Payment Modal */}
      <Modal
        isOpen={isRecordModalOpen}
        onClose={() => {
          // Reset form when closing modal
          setNewPayment({
            userRoomId: "",
            amount: 0,
            dueDate: new Date().toISOString().split("T")[0],
            status: "PENDING" as "PENDING" | "PAID" | "UPCOMING" | "OVERDUE",
            type: "RENT" as "RENT" | "UTILITIES" | "DEPOSIT" | "MAINTENANCE" | "OTHER",
            notes: "",
          });
          setIsRecordModalOpen(false);
        }}
        title="Record New Payment"
        size="lg"
        footer={
          <>
            <button
              onClick={() => {
                // Reset form when canceling
                setNewPayment({
                  userRoomId: "",
                  amount: 0,
                  dueDate: new Date().toISOString().split("T")[0],
                  status: "PENDING" as "PENDING" | "PAID" | "UPCOMING" | "OVERDUE",
                  type: "RENT" as "RENT" | "UTILITIES" | "DEPOSIT" | "MAINTENANCE" | "OTHER",
                  notes: "",
                });
                setIsRecordModalOpen(false);
              }}
              className="mr-2 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
            >
              Cancel
            </button>
            <button
              onClick={handleRecordPayment}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-500 hover:bg-amber-600 focus:outline-none"
            >
              Record Payment
            </button>
          </>
        }
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="Resident - Room"
            name="userRoomId"
            type="select"
            value={newPayment.userRoomId}
            onChange={handleInputChange}
            options={combinedUserRooms}
            required
          />
          <FormField
            label="Payment Type"
            name="type"
            type="select"
            value={newPayment.type}
            onChange={handleInputChange}
            options={paymentTypes}
            required
          />
          <FormField
            label="Amount (฿)"
            name="amount"
            type="number"
            value={newPayment.amount}
            onChange={handleInputChange}
            required
          />
          <FormField
            label="Due Date"
            name="dueDate"
            type="date"
            value={newPayment.dueDate}
            onChange={handleInputChange}
            required
          />
          <FormField
            label="Status"
            name="status"
            type="select"
            value={newPayment.status}
            onChange={handleInputChange}
            options={paymentStatuses}
            required
          />
          <div className="md:col-span-2">
            <FormField
              label="Notes"
              name="notes"
              type="textarea"
              value={newPayment.notes}
              onChange={handleInputChange}
              placeholder="Add any additional notes about this payment..."
            />
          </div>
        </div>
      </Modal>

      {/* Generate Monthly Payments Modal */}
      <Modal
        isOpen={isGenerateMonthlyModalOpen}
        onClose={() => setIsGenerateMonthlyModalOpen(false)}
        title="Generate Monthly Payments"
        size="md"
        footer={
          <>
            <button
              onClick={() => setIsGenerateMonthlyModalOpen(false)}
              className="mr-2 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
            >
              Cancel
            </button>
            <button
              onClick={handleGenerateMonthlyPayments}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 focus:outline-none"
              disabled={isLoading}
            >
              {isLoading ? "Generating..." : "Generate Payments"}
            </button>
          </>
        }
      >
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            This will generate monthly payments for all active contracts for the selected month and year.
            Only residents with active contracts will receive payment entries. Payments will be due on the 1st day of the month.
          </p>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              label="Month"
              name="month"
              type="select"
              value={monthlyPaymentForm.month}
              onChange={handleMonthlyPaymentInputChange}
              options={[
                { value: "1", label: "January" },
                { value: "2", label: "February" },
                { value: "3", label: "March" },
                { value: "4", label: "April" },
                { value: "5", label: "May" },
                { value: "6", label: "June" },
                { value: "7", label: "July" },
                { value: "8", label: "August" },
                { value: "9", label: "September" },
                { value: "10", label: "October" },
                { value: "11", label: "November" },
                { value: "12", label: "December" },
              ]}
              required
            />
            <FormField
              label="Year"
              name="year"
              type="number"
              value={monthlyPaymentForm.year}
              onChange={handleMonthlyPaymentInputChange}
              required
            />
          </div>

          <FormField
            label="Payment Type"
            name="paymentType"
            type="select"
            value={monthlyPaymentForm.paymentType}
            onChange={handleMonthlyPaymentInputChange}
            options={paymentTypes}
            required
          />
        </div>
      </Modal>

      {/* View Payment Modal */}
      <Modal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        title="Payment Details"
        size="md"
        footer={
          <div className="flex justify-between w-full">
            <button
              onClick={() => {
                if (selectedPayment) handleDeletePayment(selectedPayment);
                setIsViewModalOpen(false);
              }}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-500 hover:bg-red-600 focus:outline-none"
            >
              Delete Payment
            </button>
            <div>
              {selectedPayment?.status === "PENDING" ? (
                <button
                  onClick={() => {
                    if (selectedPayment) handleMarkAsPaid(selectedPayment);
                    setIsViewModalOpen(false);
                  }}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-500 hover:bg-green-600 focus:outline-none"
                >
                  Mark as Paid
                </button>
              ) : selectedPayment?.status === "PAID" ? (
                <button
                  onClick={() => {
                    if (selectedPayment) handleGenerateReceipt(selectedPayment);
                  }}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-500 hover:bg-amber-600 focus:outline-none"
                >
                  Generate Receipt
                </button>
              ) : null}
            </div>
          </div>
        }
      >
        {selectedPayment && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2">
                <p className="text-sm font-medium text-gray-500">Resident - Room</p>
                <p className="mt-1">{selectedPayment.user.email} - {selectedPayment.room.roomNumber}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Payment Type</p>
                <p className="mt-1">{selectedPayment.type}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Amount</p>
                <p className="mt-1 font-medium">฿{selectedPayment.amount.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Due Date</p>
                <p className="mt-1">{new Date(selectedPayment.dueDate).toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Status</p>
                <p className="mt-1">
                  <span
                    className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      selectedPayment.status === "PAID"
                        ? "bg-green-100 text-green-800"
                        : selectedPayment.status === "PENDING"
                        ? "bg-amber-100 text-amber-800"
                        : selectedPayment.status === "OVERDUE"
                        ? "bg-red-100 text-red-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {selectedPayment.status === "PAID" ? "Paid" :
                     selectedPayment.status === "PENDING" ? "Pending" :
                     selectedPayment.status === "UPCOMING" ? "Upcoming" :
                     selectedPayment.status === "OVERDUE" ? "Overdue" : selectedPayment.status}
                  </span>
                </p>
              </div>
            </div>

            {selectedPayment.status === "PAID" && (
              <div className="grid grid-cols-2 gap-4 border-t border-gray-200 pt-4 mt-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Paid Date</p>
                  <p className="mt-1">{selectedPayment.paidDate && new Date(selectedPayment.paidDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Receipt Number</p>
                  <p className="mt-1">{selectedPayment.receiptNumber || "N/A"}</p>
                </div>
                {selectedPayment.paymentMethod && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Payment Method</p>
                    <p className="mt-1">{selectedPayment.paymentMethod}</p>
                  </div>
                )}
              </div>
            )}

            {selectedPayment.notes && (
              <div className="border-t border-gray-200 pt-4 mt-4">
                <p className="text-sm font-medium text-gray-500">Notes</p>
                <p className="mt-1 text-sm text-gray-600">{selectedPayment.notes}</p>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleteConfirmModalOpen}
        onClose={() => setIsDeleteConfirmModalOpen(false)}
        title="Confirm Delete Payment"
        size="md"
        footer={
          <>
            <button
              onClick={() => setIsDeleteConfirmModalOpen(false)}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
            >
              Cancel
            </button>
            <button
              onClick={confirmDeletePayment}
              className="ml-2 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-500 hover:bg-red-600 focus:outline-none"
            >
              Delete Payment
            </button>
          </>
        }
      >
        {selectedPayment && (
          <div className="space-y-4">
            <div className="flex items-center justify-center text-red-500 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </div>
            <p className="text-center text-gray-700">
              Are you sure you want to delete this payment?
            </p>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="col-span-2">
                  <span className="font-medium text-gray-500">Resident - Room:</span> {selectedPayment.user.email} - {selectedPayment.room.roomNumber}
                </div>
                <div>
                  <span className="font-medium text-gray-500">Amount:</span> ฿{selectedPayment.amount.toLocaleString()}
                </div>
                <div>
                  <span className="font-medium text-gray-500">Type:</span> {selectedPayment.type}
                </div>
                <div>
                  <span className="font-medium text-gray-500">Due Date:</span> {new Date(selectedPayment.dueDate).toLocaleDateString()}
                </div>
                <div>
                  <span className="font-medium text-gray-500">Status:</span> {selectedPayment.status}
                </div>
              </div>
            </div>
            <p className="text-red-600 text-sm font-medium">
              This action cannot be undone. The payment will be permanently deleted from the system.
            </p>
          </div>
        )}
      </Modal>
    </div>
  );
}
