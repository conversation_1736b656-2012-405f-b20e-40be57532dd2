import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';
import bcrypt from 'bcrypt';

// GET a specific user by ID
async function handleGet(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        address: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        rooms: {
          select: {
            id: true,
            roomNumber: true,
            roomType: {
              select: {
                name: true,
                price: true
              }
            },
            isOccupied: true
          }
        },
        payments: {
          select: {
            id: true,
            amount: true,
            dueDate: true,
            paidDate: true,
            status: true,
            type: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        },
        serviceRequests: {
          select: {
            id: true,
            title: true,
            status: true,
            priority: true,
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error(`Error fetching user with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PUT update a user
async function handlePut(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { name, email, phone, address, role, password } = body;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    });

    if (!existingUser) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // If email is being changed, check if it's already in use
    if (email && email !== existingUser.email) {
      const duplicateEmail = await prisma.user.findUnique({
        where: { email }
      });

      if (duplicateEmail) {
        return NextResponse.json(
          { success: false, message: 'Email already in use by another user' },
          { status: 409 }
        );
      }
    }

    // Validate role if provided
    if (role) {
      const validRoles = ['ADMIN', 'RESIDENT', 'VISITORS'];
      if (!validRoles.includes(role)) {
        return NextResponse.json(
          { success: false, message: 'Invalid role' },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};
    
    if (name) updateData.name = name;
    if (email) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone || null;
    if (address !== undefined) updateData.address = address || null;
    if (role) updateData.role = role;

    // Hash password if provided
    if (password) {
      const saltRounds = 10;
      updateData.password = await bcrypt.hash(password, saltRounds);
    }

    // Update the user
    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        address: true,
        role: true,
        createdAt: true,
        updatedAt: true
      }
    });

    return NextResponse.json({
      success: true,
      message: 'User updated successfully',
      data: updatedUser
    });
  } catch (error) {
    console.error(`Error updating user with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE a user
async function handleDelete(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
      include: {
        rooms: true,
        payments: true,
        serviceRequests: true
      }
    });

    if (!existingUser) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has associated data
    if (existingUser.rooms.length > 0 || existingUser.payments.length > 0 || existingUser.serviceRequests.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Cannot delete user with associated data. Please remove or reassign the data first.' 
        },
        { status: 400 }
      );
    }

    // Delete the user
    await prisma.user.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting user with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete user' },
      { status: 500 }
    );
  }
}

// Export the handlers with authentication middleware
export const GET = withAuth(handleGet, ['ADMIN']);
export const PUT = withAuth(handlePut, ['ADMIN']);
export const DELETE = withAuth(handleDelete, ['ADMIN']);
