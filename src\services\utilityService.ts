/**
 * Service for handling utility readings and billing API calls
 */

// Get all utility readings with optional filtering
export async function getUtilityReadings(params: {
  roomId?: string;
  isPaid?: boolean;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}) {
  const queryParams = new URLSearchParams();
  
  if (params.roomId) {
    queryParams.append('roomId', params.roomId);
  }
  
  if (params.isPaid !== undefined) {
    queryParams.append('isPaid', params.isPaid.toString());
  }
  
  if (params.startDate) {
    queryParams.append('startDate', params.startDate);
  }
  
  if (params.endDate) {
    queryParams.append('endDate', params.endDate);
  }
  
  if (params.page) {
    queryParams.append('page', params.page.toString());
  }
  
  if (params.limit) {
    queryParams.append('limit', params.limit.toString());
  }
  
  const response = await fetch(`/api/utility-readings?${queryParams.toString()}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }
  });
  
  return response.json();
}

// Get a specific utility reading by ID
export async function getUtilityReadingById(id: string) {
  const response = await fetch(`/api/utility-readings/${id}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }
  });
  
  return response.json();
}

// Create a new utility reading
export async function createUtilityReading(data: {
  roomId: string;
  waterReading: number;
  electricityReading: number;
  readingDate?: string;
  waterUsage?: number;
  electricityUsage?: number;
  waterCost?: number;
  electricityCost?: number;
  totalCost?: number;
  isPaid?: boolean;
}) {
  const response = await fetch('/api/utility-readings', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify(data)
  });
  
  return response.json();
}

// Update an existing utility reading
export async function updateUtilityReading(id: string, data: {
  waterReading?: number;
  electricityReading?: number;
  readingDate?: string;
  waterUsage?: number;
  electricityUsage?: number;
  waterCost?: number;
  electricityCost?: number;
  totalCost?: number;
  isPaid?: boolean;
}) {
  const response = await fetch(`/api/utility-readings/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify(data)
  });
  
  return response.json();
}

// Delete a utility reading
export async function deleteUtilityReading(id: string) {
  const response = await fetch(`/api/utility-readings/${id}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }
  });
  
  return response.json();
}

// Get utility readings for a specific room
export async function getUtilityReadingsByRoom(roomId: string, params?: {
  isPaid?: boolean;
  startDate?: string;
  endDate?: string;
  limit?: number;
}) {
  const queryParams = new URLSearchParams();
  
  if (params?.isPaid !== undefined) {
    queryParams.append('isPaid', params.isPaid.toString());
  }
  
  if (params?.startDate) {
    queryParams.append('startDate', params.startDate);
  }
  
  if (params?.endDate) {
    queryParams.append('endDate', params.endDate);
  }
  
  if (params?.limit) {
    queryParams.append('limit', params.limit.toString());
  }
  
  const response = await fetch(`/api/utility-readings/room/${roomId}?${queryParams.toString()}`, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }
  });
  
  return response.json();
}

// Generate a bill from utility readings
export async function generateBill(data: {
  readingIds: string[];
  dueDate: string;
  notes?: string;
}) {
  const response = await fetch('/api/utility-readings/generate-bill', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify(data)
  });
  
  return response.json();
}

// Get all rooms (for utility reading form)
export async function getRooms() {
  const response = await fetch('/api/rooms', {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    }
  });
  
  return response.json();
}
