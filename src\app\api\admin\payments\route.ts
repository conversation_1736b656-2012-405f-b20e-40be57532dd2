import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

// GET all payments with filtering options
async function GET(request: NextRequest) {
  try {
    console.log('GET /api/admin/payments - Request received');

    // Get the authenticated user from the request
    const user = (request as any).user;
    console.log('Authenticated user:', user ? { id: user.id, role: user.role } : 'No user');

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      console.log('Unauthorized access attempt - Role:', user?.role);
      return NextResponse.json(
        { success: false, message: 'Unauthorized - Admin role required' },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const type = url.searchParams.get('type');
    const userId = url.searchParams.get('userId');
    const roomId = url.searchParams.get('roomId');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const month = url.searchParams.get('month');
    const year = url.searchParams.get('year');

    console.log('Query parameters:', { status, type, userId, roomId, startDate, endDate, month, year });

    // Build the where clause for filtering
    const where: any = {};

    if (status) {
      where.status = status.toUpperCase();
    }

    if (type) {
      where.type = type.toUpperCase();
    }

    if (userId) {
      where.userId = userId;
    }

    if (roomId) {
      where.roomId = roomId;
    }

    // Filter by month and year if provided
    if (month !== null && year !== null) {
      const monthNum = parseInt(month);
      const yearNum = parseInt(year);

      // Create date range for the specified month
      const startOfMonth = new Date(yearNum, monthNum, 1);
      const endOfMonth = new Date(yearNum, monthNum + 1, 0); // Last day of month

      where.dueDate = {
        gte: startOfMonth,
        lte: endOfMonth,
      };

      console.log(`Filtering by month: ${monthNum}, year: ${yearNum}`);
      console.log(`Date range: ${startOfMonth.toISOString()} to ${endOfMonth.toISOString()}`);
    }
    // Otherwise use start/end date if provided
    else if (startDate && endDate) {
      where.dueDate = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      where.dueDate = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      where.dueDate = {
        lte: new Date(endDate),
      };
    }

    console.log('Prisma query where clause:', where);

    // Check if Prisma client is initialized
    if (!prisma) {
      console.error('Prisma client is not initialized');
      return NextResponse.json(
        { success: false, message: 'Database connection error' },
        { status: 500 }
      );
    }

    // Get all payments with filters
    const payments = await prisma.payment.findMany({
      where,
      orderBy: {
        dueDate: 'desc',
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        room: {
          select: {
            id: true,
            roomNumber: true,
            roomType: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    console.log(`Found ${payments.length} payments`);

    // Format the response
    const formattedPayments = payments.map(payment => ({
      id: payment.id,
      amount: payment.amount,
      dueDate: payment.dueDate,
      paidDate: payment.paidDate,
      status: payment.status,
      type: payment.type,
      notes: payment.notes,
      receiptNumber: payment.receiptNumber,
      receiptUrl: payment.receiptUrl,
      paymentMethod: payment.paymentMethod,
      user: {
        id: payment.user.id,
        name: payment.user.name,
        email: payment.user.email,
      },
      room: {
        id: payment.room.id,
        roomNumber: payment.room.roomNumber,
        roomType: payment.room.roomType.name,
      },
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
    }));

    return NextResponse.json({
      success: true,
      data: formattedPayments,
    });
  } catch (error) {
    console.error('Error fetching payments:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch payments',
        error: errorMessage
      },
      { status: 500 }
    );
  }
}

// POST to create a new payment
async function POST(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();
    const {
      userId,
      roomId,
      amount,
      dueDate,
      status,
      type,
      notes,
      paidDate,
      paymentMethod,
      receiptNumber
    } = body;

    // Validate required fields
    if (!userId || !roomId || !amount || !dueDate || !status || !type) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create the payment
    const payment = await prisma.payment.create({
      data: {
        userId,
        roomId,
        amount: parseFloat(amount),
        dueDate: new Date(dueDate),
        status: status.toUpperCase(),
        type: type.toUpperCase(),
        notes,
        paidDate: paidDate ? new Date(paidDate) : null,
        paymentMethod,
        receiptNumber,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        room: {
          select: {
            id: true,
            roomNumber: true,
            roomType: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    // Create a notification for the user
    await prisma.notification.create({
      data: {
        userId,
        title: `New ${type.toLowerCase()} payment`,
        message: `A new ${type.toLowerCase()} payment of ฿${amount} has been added to your account, due on ${new Date(dueDate).toLocaleDateString()}.`,
        type: 'PAYMENT_DUE',
        isRead: false,
        link: '/user/dashboard/payments',
      },
    });

    // Format the response
    const formattedPayment = {
      id: payment.id,
      amount: payment.amount,
      dueDate: payment.dueDate,
      paidDate: payment.paidDate,
      status: payment.status,
      type: payment.type,
      notes: payment.notes,
      receiptNumber: payment.receiptNumber,
      receiptUrl: payment.receiptUrl,
      paymentMethod: payment.paymentMethod,
      user: {
        id: payment.user.id,
        name: payment.user.name,
        email: payment.user.email,
      },
      room: {
        id: payment.room.id,
        roomNumber: payment.room.roomNumber,
        roomType: payment.room.roomType.name,
      },
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
    };

    return NextResponse.json({
      success: true,
      data: formattedPayment,
    });
  } catch (error) {
    console.error('Error creating payment:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create payment' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedGET = withAuth(GET, ['ADMIN']);
const protectedPOST = withAuth(POST, ['ADMIN']);

export { protectedGET as GET, protectedPOST as POST };
