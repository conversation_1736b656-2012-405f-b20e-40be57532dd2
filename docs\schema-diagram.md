# Dormitory Management System - Database Schema

## Overview

This document provides a visual representation of the database schema for the Dormitory Management System. The schema is designed to support all the features of the application, including user management, room management, payments, service requests, utility readings, and activity logging.

## Entity Relationship Diagram

```mermaid
erDiagram
    User ||--o{ Room : "resides in"
    User ||--o{ Payment : "makes"
    User ||--o{ ServiceRequest : "submits"
    User ||--o{ Activity : "performs"
    User ||--o{ Notification : "receives"
    
    Room ||--o{ Payment : "associated with"
    Room ||--o{ ServiceRequest : "has"
    Room ||--o{ UtilityReading : "has"
    Room ||--o{ RoomImage : "has"
    
    ServiceRequest ||--o{ ServiceResponse : "has"
    ServiceRequest ||--o{ ServiceRequestImage : "has"
    
    Contract }|--|| User : "belongs to"
    Contract }|--|| Room : "for"
    
    User {
        string id PK
        string email UK
        string password
        string name
        enum role
        string phone
        string profileImage
        string address
        string emergencyContact
        datetime createdAt
        datetime updatedAt
    }
    
    Room {
        string id PK
        string roomNumber UK
        string roomType
        int floor
        int capacity
        float monthlyRate
        boolean isOccupied
        string description
        string amenities
        datetime createdAt
        datetime updatedAt
    }
    
    RoomImage {
        string id PK
        string url
        string roomId FK
        datetime createdAt
    }
    
    Payment {
        string id PK
        float amount
        datetime dueDate
        datetime paidDate
        enum status
        enum type
        string notes
        string receiptNumber
        string receiptUrl
        string paymentMethod
        string userId FK
        string roomId FK
        datetime createdAt
        datetime updatedAt
    }
    
    ServiceRequest {
        string id PK
        enum type
        string title
        string description
        enum status
        enum priority
        string userId FK
        string roomId FK
        string assignedTo
        datetime createdAt
        datetime updatedAt
        datetime resolvedAt
    }
    
    ServiceRequestImage {
        string id PK
        string url
        string serviceRequestId FK
        datetime createdAt
    }
    
    ServiceResponse {
        string id PK
        string message
        string attachments
        string serviceRequestId FK
        datetime createdAt
        datetime updatedAt
    }
    
    UtilityReading {
        string id PK
        string roomId FK
        float waterReading
        float electricityReading
        datetime readingDate
        float waterUsage
        float electricityUsage
        float waterCost
        float electricityCost
        float totalCost
        boolean isPaid
        datetime createdAt
        datetime updatedAt
    }
    
    Activity {
        string id PK
        string userId FK
        string performerId FK
        string action
        string details
        string target
        string targetId
        enum targetType
        string ipAddress
        datetime timestamp
    }
    
    Notification {
        string id PK
        string userId FK
        string title
        string message
        enum type
        boolean isRead
        string link
        datetime createdAt
    }
    
    Announcement {
        string id PK
        string title
        string content
        boolean isPinned
        string attachments
        datetime createdAt
        datetime updatedAt
    }
    
    Settings {
        string id PK
        string key UK
        string value
        string description
        datetime updatedAt
    }
    
    Contract {
        string id PK
        string residentId FK
        string roomId FK
        datetime startDate
        datetime endDate
        float rentAmount
        float depositAmount
        boolean isActive
        datetime terminationDate
        string terminationReason
        string documentUrl
        datetime createdAt
        datetime updatedAt
    }
```

## Enumerations

### UserRole
- ADMIN
- STAFF
- RESIDENT

### PaymentStatus
- PAID
- PENDING
- UPCOMING
- OVERDUE
- CANCELLED

### PaymentType
- RENT
- UTILITIES
- DEPOSIT
- MAINTENANCE
- OTHER

### ServiceRequestType
- MAINTENANCE
- CLEANING
- ELECTRICAL
- PLUMBING
- FURNITURE
- APPLIANCE
- INTERNET
- SECURITY
- OTHER

### ServiceRequestStatus
- PENDING
- IN_PROGRESS
- RESOLVED
- CANCELLED

### Priority
- LOW
- MEDIUM
- HIGH
- URGENT

### TargetType
- USER
- ROOM
- PAYMENT
- SERVICE_REQUEST
- UTILITY
- SYSTEM

### NotificationType
- PAYMENT_DUE
- PAYMENT_RECEIVED
- SERVICE_REQUEST_UPDATE
- ANNOUNCEMENT
- SYSTEM

## Key Relationships

1. **User to Room**: Many-to-many relationship. A user (resident) can be assigned to multiple rooms, and a room can have multiple residents (e.g., in shared accommodations).

2. **User to Payment**: One-to-many relationship. A user can make multiple payments.

3. **Room to Payment**: One-to-many relationship. A room can have multiple payments associated with it.

4. **User to ServiceRequest**: One-to-many relationship. A user can submit multiple service requests.

5. **Room to ServiceRequest**: One-to-many relationship. A room can have multiple service requests.

6. **ServiceRequest to ServiceResponse**: One-to-many relationship. A service request can have multiple responses.

7. **Room to UtilityReading**: One-to-many relationship. A room can have multiple utility readings over time.

8. **User to Activity**: One-to-many relationship. A user can perform multiple activities, and activities can be performed on behalf of users.

9. **User to Notification**: One-to-many relationship. A user can receive multiple notifications.

10. **Contract to User and Room**: A contract belongs to a specific user (resident) and is for a specific room.

## Notes

- All models use UUID as primary keys for better security and distribution.
- Timestamps (createdAt, updatedAt) are included in most models for auditing purposes.
- Soft deletion is not implemented at the database level but can be added with an `isDeleted` boolean field if needed.
- The schema supports image uploads for rooms and service requests.
- Activity logging is comprehensive, tracking all important actions in the system.
- Notifications system allows for various types of alerts to users.
- Settings model provides a flexible way to store system-wide configuration.
