import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

// GET all activities with filtering options
async function GET(request: NextRequest) {
  try {
    console.log('GET /api/admin/activities - Request received');

    // Get the authenticated user from the request
    const user = (request as any).user;
    console.log('Authenticated user:', user ? { id: user.id, role: user.role } : 'No user');

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      console.log('Unauthorized access attempt - Role:', user?.role);
      return NextResponse.json(
        { success: false, message: 'Unauthorized - Admin role required' },
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const targetType = searchParams.get('targetType');
    const search = searchParams.get('search');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const userId = searchParams.get('userId');

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build the where clause for filtering
    const where: any = {};

    // Filter by target type
    if (targetType && targetType !== 'All') {
      where.targetType = targetType;
    }

    // Filter by user ID
    if (userId) {
      where.userId = userId;
    }

    // Filter by date range
    if (startDate) {
      where.timestamp = {
        ...where.timestamp,
        gte: new Date(startDate),
      };
    }

    if (endDate) {
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999); // End of the day
      where.timestamp = {
        ...where.timestamp,
        lte: endDateTime,
      };
    }

    // Search in user name, action, details, or target
    if (search) {
      where.OR = [
        {
          user: {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          action: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          details: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          target: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Get activities with pagination and filtering
    const [activities, totalCount] = await Promise.all([
      prisma.activity.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
          performer: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
        },
        orderBy: {
          timestamp: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.activity.count({ where }),
    ]);

    // Format the activities for the response
    const formattedActivities = activities.map((activity) => ({
      id: activity.id,
      user: activity.user.name,
      userId: activity.userId,
      userRole: activity.user.role,
      performer: activity.performer?.name,
      performerId: activity.performerId,
      action: activity.action,
      details: activity.details,
      target: activity.target,
      targetId: activity.targetId,
      targetType: activity.targetType,
      ipAddress: activity.ipAddress,
      timestamp: activity.timestamp,
    }));

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: formattedActivities,
      meta: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
      },
    });
  } catch (error) {
    console.error('Error fetching activities:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch activities' },
      { status: 500 }
    );
  }
}

// POST to create a new activity
async function POST(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user is authenticated
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();
    const {
      userId,
      action,
      details,
      target,
      targetId,
      targetType,
      ipAddress,
    } = body;

    // Validate required fields
    if (!userId || !action) {
      return NextResponse.json(
        { success: false, message: 'User ID and action are required' },
        { status: 400 }
      );
    }

    // Create the activity
    const activity = await prisma.activity.create({
      data: {
        userId,
        performerId: user.id, // The authenticated user is the performer
        action,
        details,
        target,
        targetId,
        targetType,
        ipAddress,
      },
    });

    return NextResponse.json({
      success: true,
      data: activity,
    });
  } catch (error) {
    console.error('Error creating activity:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create activity' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
// GET - Only admins can view all activities
const protectedGET = withAuth(GET, ['ADMIN']);
// POST - Any authenticated user can create activities
const protectedPOST = withAuth(POST, ['ADMIN', 'RESIDENT', 'VISITORS']);

export { protectedGET as GET, protectedPOST as POST };
