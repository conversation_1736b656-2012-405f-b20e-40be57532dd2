"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';

interface ContractData {
  id: string;
  residentId: string;
  roomId: string;
  startDate: string;
  endDate: string;
  rentAmount: number;
  depositAmount: number;
  isActive: boolean;
  room: {
    id: string;
    roomNumber: string;
    roomType: {
      name: string;
      price: number;
    };
  };
}

export default function PaymentPage({ params }: { params: { contractId: string } }) {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [contractData, setContractData] = useState<ContractData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    // Redirect if user is not logged in
    if (!authLoading && !user) {
      router.push('/auth?redirect=' + encodeURIComponent(`/payment/${params.contractId}`));
      return;
    }

    const fetchContractData = async () => {
      try {
        // Get token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          setError('Authentication token not found. Please log in again.');
          setIsLoading(false);
          return;
        }

        const contractId = params.contractId;
        const response = await fetch(`/api/contracts/${contractId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          // Log detailed error information
          const errorText = await response.text();
          console.error('Contract API error:', {
            status: response.status,
            statusText: response.statusText,
            body: errorText
          });

          setError(`Failed to fetch contract data: ${response.status} ${response.statusText}`);
          setIsLoading(false);
          return;
        }

        const result = await response.json();

        if (result.success) {
          setContractData(result.data);
        } else {
          setError(result.message || 'Failed to fetch contract data');
        }
      } catch (err) {
        setError('An error occurred while fetching contract data');
        console.error('Contract fetch error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchContractData();
    }
  }, [params.contractId, user, authLoading, router]);

  const handlePayment = async () => {
    if (!user || !contractData) return;

    setIsProcessing(true);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        setError('Authentication token not found. Please log in again.');
        setIsProcessing(false);
        return;
      }

      // Create checkout session
      const response = await fetch('/api/payment/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          contractId: contractData.id,
          items: [
            {
              name: `First Month Rent - ${contractData.room.roomType.name} (${contractData.room.roomNumber})`,
              amount: contractData.rentAmount,
              quantity: 1
            },
            {
              name: `Security Deposit - ${contractData.room.roomType.name} (${contractData.room.roomNumber})`,
              amount: contractData.depositAmount,
              quantity: 1
            }
          ]
        })
      });

      if (!response.ok) {
        // Log detailed error information
        const errorText = await response.text();
        console.error('Checkout session error:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        });

        setError(`Failed to create checkout session: ${response.status} ${response.statusText}`);
        setIsProcessing(false);
        return;
      }

      const result = await response.json();

      if (result.success) {
        // Redirect to Stripe checkout
        window.location.href = result.url;
      } else {
        setError(result.message || 'Failed to create payment session');
        setIsProcessing(false);
      }
    } catch (err) {
      setError('An error occurred while processing payment');
      console.error('Payment error:', err);
      setIsProcessing(false);
    }
  };

  // For demo purposes, let's add a function to simulate successful payment
  const handleDemoPayment = async () => {
    if (!user || !contractData) return;

    setIsProcessing(true);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        setError('Authentication token not found. Please log in again.');
        setIsProcessing(false);
        return;
      }

      // Update contract to active
      const contractResponse = await fetch(`/api/contracts/${contractData.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          isActive: true
        })
      });

      if (!contractResponse.ok) {
        // Log detailed error information
        const errorText = await contractResponse.text();
        console.error('Contract update error:', {
          status: contractResponse.status,
          statusText: contractResponse.statusText,
          body: errorText
        });

        setError(`Failed to update contract: ${contractResponse.status} ${contractResponse.statusText}`);
        setIsProcessing(false);
        return;
      }

      const contractResult = await contractResponse.json();

      if (contractResult.success) {
        // Create payment records
        const paymentResponse = await fetch('/api/payment/demo-success', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            contractId: contractData.id,
            userId: user.id,
            roomId: contractData.roomId,
            rentAmount: contractData.rentAmount,
            depositAmount: contractData.depositAmount
          })
        });

        if (!paymentResponse.ok) {
          // Log detailed error information
          const errorText = await paymentResponse.text();
          console.error('Payment processing error:', {
            status: paymentResponse.status,
            statusText: paymentResponse.statusText,
            body: errorText
          });

          setError(`Failed to process payment: ${paymentResponse.status} ${paymentResponse.statusText}`);
          setIsProcessing(false);
          return;
        }

        const paymentResult = await paymentResponse.json();

        if (paymentResult.success) {
          // Redirect to success page
          router.push(`/payment/success?session_id=demo_${Date.now()}`);
        } else {
          setError(paymentResult.message || 'Failed to process demo payment');
          setIsProcessing(false);
        }
      } else {
        setError(contractResult.message || 'Failed to update contract');
        setIsProcessing(false);
      }
    } catch (err) {
      setError('An error occurred while processing demo payment');
      console.error('Demo payment error:', err);
      setIsProcessing(false);
    }
  };

  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !contractData) {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-8">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-700">{error || 'Contract not found'}</p>
          <button
            onClick={() => router.push('/rooms')}
            className="mt-6 bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition"
          >
            Back to Rooms
          </button>
        </div>
      </div>
    );
  }

  // Format dates
  const startDate = new Date(contractData.startDate).toLocaleDateString();
  const endDate = new Date(contractData.endDate).toLocaleDateString();

  // Calculate total amount
  const totalAmount = contractData.rentAmount + contractData.depositAmount;

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-8">
        <h1 className="text-3xl font-bold mb-6 text-center">Payment Details</h1>

        <div className="mb-8 p-4 bg-blue-50 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Contract Summary</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-600">Room Type:</p>
              <p className="font-medium">{contractData.room.roomType.name}</p>
            </div>
            <div>
              <p className="text-gray-600">Room Number:</p>
              <p className="font-medium">{contractData.room.roomNumber}</p>
            </div>
            <div>
              <p className="text-gray-600">Lease Period:</p>
              <p className="font-medium">{startDate} to {endDate}</p>
            </div>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Payment Summary</h2>
          <div className="border-t border-b py-4">
            <div className="flex justify-between py-2">
              <span>First Month Rent</span>
              <span>฿{contractData.rentAmount.toLocaleString()}</span>
            </div>
            <div className="flex justify-between py-2">
              <span>Security Deposit</span>
              <span>฿{contractData.depositAmount.toLocaleString()}</span>
            </div>
            <div className="flex justify-between py-2 font-bold">
              <span>Total Amount</span>
              <span>฿{totalAmount.toLocaleString()}</span>
            </div>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Payment Method</h2>
          <p className="mb-4">
            We accept the following payment methods through our secure payment processor, Stripe:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="p-3 border rounded-lg flex items-center space-x-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
              <span>Credit/Debit Cards</span>
            </div>
            <div className="p-3 border rounded-lg flex items-center space-x-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <span>PromptPay</span>
            </div>
            <div className="p-3 border rounded-lg flex items-center space-x-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
              </svg>
              <span>Bank Transfer</span>
            </div>
          </div>
          <p className="text-sm text-gray-600">
            You'll be redirected to Stripe's secure payment page to complete your transaction.
          </p>
        </div>

        <div className="flex justify-between">
          <button
            onClick={() => router.back()}
            className="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-400 transition"
            disabled={isProcessing}
          >
            Back
          </button>

          {/* For production, use this button */}
          <button
            onClick={handlePayment}
            disabled={isProcessing}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition"
          >
            {isProcessing ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
            ) : (
              'Pay Now'
            )}
          </button>

          {/* For demo purposes, use this button */}
          <button
            onClick={handleDemoPayment}
            disabled={isProcessing}
            className="px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition"
          >
            {isProcessing ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
            ) : (
              'Demo Payment (Skip Stripe)'
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
