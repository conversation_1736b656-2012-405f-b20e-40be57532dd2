import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET service requests for a specific user
async function handleGet(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params;
    const requestUserId = request.headers.get('userId');
    const userRole = request.headers.get('userRole');
    
    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;
    
    // Only admins or the user themselves can view their service requests
    if (userRole !== 'ADMIN' && requestUserId !== userId) {
      return NextResponse.json(
        { success: false, message: 'You do not have permission to view these service requests' },
        { status: 403 }
      );
    }

    // Build the where clause
    const where: any = { userId };
    
    if (status) {
      where.status = status;
    }
    
    // Get total count for pagination
    const totalCount = await prisma.serviceRequest.count({ where });
    
    // Get service requests for the user
    const serviceRequests = await prisma.serviceRequest.findMany({
      where,
      include: {
        room: {
          select: {
            roomNumber: true,
            roomType: {
              select: {
                name: true
              }
            }
          }
        },
        responses: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 1 // Get only the latest response
        },
        images: {
          select: {
            id: true,
            url: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    
    return NextResponse.json({
      success: true,
      data: serviceRequests,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages
      }
    });
  } catch (error) {
    console.error(`Error fetching service requests for user with ID ${params.userId}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch service requests' },
      { status: 500 }
    );
  }
}

// Export the handlers with authentication middleware
export const GET = withAuth(handleGet, ['ADMIN', 'RESIDENT']);
