import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

// POST to generate monthly payments for all active contracts
async function POST(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user is an admin
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();
    const { month, year, paymentType = 'RENT' } = body;

    // Validate required fields
    if (!month || !year) {
      return NextResponse.json(
        { success: false, message: 'Month and year are required' },
        { status: 400 }
      );
    }

    // Convert month and year to a date object for the first day of the month
    const targetMonth = parseInt(month, 10) - 1; // JavaScript months are 0-indexed
    const targetYear = parseInt(year, 10);
    const dueDate = new Date(targetYear, targetMonth, 1);

    // Get all active contracts
    const activeContracts = await prisma.contract.findMany({
      where: {
        isActive: true,
        startDate: {
          lte: dueDate, // Contract started before or on the target month
        },
        endDate: {
          gte: dueDate, // Contract ends after the target month
        },
      },
      include: {
        resident: true,
        room: true,
      },
    });

    // Filter out contracts with null resident or room
    const validContracts = activeContracts.filter(
      contract => contract.resident && contract.room
    );

    if (validContracts.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No valid active contracts found',
      }, { status: 404 });
    }

    // Check if payments already exist for this month
    const existingPayments = await prisma.payment.findMany({
      where: {
        dueDate: {
          gte: new Date(targetYear, targetMonth, 1),
          lt: new Date(targetYear, targetMonth + 1, 1),
        },
        type: paymentType,
      },
    });

    // Create a map of existing payments by userId and roomId
    const existingPaymentMap = new Map();
    existingPayments.forEach(payment => {
      const key = `${payment.userId}-${payment.roomId}`;
      existingPaymentMap.set(key, payment);
    });

    // Create payments for each active contract
    const createdPayments = [];
    const skippedPayments = [];

    for (const contract of validContracts) {
      const key = `${contract.residentId}-${contract.roomId}`;

      // Skip if payment already exists for this user and room in the target month
      if (existingPaymentMap.has(key)) {
        skippedPayments.push({
          userId: contract.residentId,
          userName: contract.resident.name,
          roomNumber: contract.room.roomNumber,
          reason: 'Payment already exists for this month',
        });
        continue;
      }

      // Create a new payment
      const payment = await prisma.payment.create({
        data: {
          userId: contract.residentId,
          roomId: contract.roomId,
          amount: contract.rentAmount,
          dueDate: new Date(targetYear, targetMonth, 1), // Due on the 1st day of the month
          status: 'PENDING',
          type: paymentType,
          notes: `Monthly ${paymentType.toLowerCase()} payment for ${new Date(targetYear, targetMonth).toLocaleString('default', { month: 'long', year: 'numeric' })}`,
        },
        include: {
          user: {
            select: {
              name: true,
            },
          },
          room: {
            select: {
              roomNumber: true,
            },
          },
        },
      });

      // Create a notification for the user
      try {
        await prisma.notification.create({
          data: {
            userId: contract.residentId,
            title: `New Monthly Payment`,
            message: `Your ${paymentType.toLowerCase()} payment of ฿${contract.rentAmount} for ${new Date(targetYear, targetMonth).toLocaleString('default', { month: 'long', year: 'numeric' })} is now due on the 1st.`,
            type: 'PAYMENT_DUE' as any,
            isRead: false,
            link: '/user/dashboard/payments',
          },
        });
      } catch (notificationError) {
        console.warn('Failed to create notification, but payment was created:', notificationError);
        // Continue with the process even if notification creation fails
      }

      createdPayments.push({
        id: payment.id,
        userId: payment.userId,
        userName: payment.user.name,
        roomNumber: payment.room.roomNumber,
        amount: payment.amount,
        dueDate: payment.dueDate,
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        created: createdPayments,
        skipped: skippedPayments,
        totalCreated: createdPayments.length,
        totalSkipped: skippedPayments.length,
      },
    });
  } catch (error) {
    console.error('Error generating monthly payments:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to generate monthly payments' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedPOST = withAuth(POST, ['ADMIN']);

export { protectedPOST as POST };
