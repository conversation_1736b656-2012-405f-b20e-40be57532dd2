"use client";

import { motion } from 'framer-motion';
import DataTable from '@/components/admin/DataTable';

interface Request {
  id: number;
  type: string;
  user: string;
  room: string;
  status: string;
  date: string;
  priority: string;
}

interface PendingRequestsProps {
  requests: Request[];
}

export default function PendingRequests({ requests }: PendingRequestsProps) {
  const columns = [
    { key: 'type', header: 'Type' },
    { key: 'room', header: 'Room' },
    { 
      key: 'status', 
      header: 'Status',
      render: (value: string) => (
        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
          value === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'
        }`}>
          {value}
        </span>
      )
    },
    { 
      key: 'priority', 
      header: 'Priority',
      render: (value: string) => (
        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
          value === 'High' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
        }`}>
          {value}
        </span>
      )
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-semibold">Pending Requests</h2>
        <Link href="/cms/admin/requests" className="text-amber-600 text-sm hover:underline">
          View All
        </Link>
      </div>
      <DataTable data={requests} columns={columns} />
    </div>
  );
}
