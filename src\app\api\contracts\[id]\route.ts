import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// GET a specific contract by ID
async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Extract the ID from params
    const { id } = await context.params;

    const contract = await prisma.contract.findUnique({
      where: { id },
      include: {
        room: {
          select: {
            id: true,
            roomNumber: true,
            roomType: {
              select: {
                name: true,
                price: true
              }
            }
          }
        }
      }
    });

    if (!contract) {
      return NextResponse.json(
        { success: false, message: 'Contract not found' },
        { status: 404 }
      );
    }

    // Check if the user is authorized to view this contract
    const user = (request as any).user;

    // Allow ADMIN to view all contracts
    // Allow users to view their own contracts
    if (user.role !== 'ADMIN' && contract.residentId !== user.id) {
      console.log('Authorization failed:', {
        userRole: user.role,
        userId: user.id,
        contractResidentId: contract.residentId
      });

      return NextResponse.json(
        { success: false, message: 'Unauthorized to view this contract' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      data: contract
    });
  } catch (error) {
    console.error(`Error fetching contract:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch contract' },
      { status: 500 }
    );
  }
}

// PUT update a contract
async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Extract the ID from params
    const { id } = await context.params;
    const body = await request.json();
    const {
      startDate,
      endDate,
      rentAmount,
      depositAmount,
      isActive,
      terminationDate,
      terminationReason,
      documentUrl
    } = body;

    // Check if contract exists
    const existingContract = await prisma.contract.findUnique({
      where: { id }
    });

    if (!existingContract) {
      return NextResponse.json(
        { success: false, message: 'Contract not found' },
        { status: 404 }
      );
    }

    // Update the contract
    const updatedContract = await prisma.contract.update({
      where: { id },
      data: {
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        rentAmount: rentAmount !== undefined ? parseFloat(rentAmount.toString()) : undefined,
        depositAmount: depositAmount !== undefined ? parseFloat(depositAmount.toString()) : undefined,
        isActive: isActive !== undefined ? isActive : undefined,
        terminationDate: terminationDate ? new Date(terminationDate) : undefined,
        terminationReason: terminationReason !== undefined ? terminationReason : undefined,
        documentUrl: documentUrl !== undefined ? documentUrl : undefined
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Contract updated successfully',
      data: updatedContract
    });
  } catch (error) {
    console.error(`Error updating contract:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to update contract' },
      { status: 500 }
    );
  }
}

// DELETE a contract
async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Extract the ID from params
    const { id } = await context.params;

    // Check if contract exists
    const existingContract = await prisma.contract.findUnique({
      where: { id }
    });

    if (!existingContract) {
      return NextResponse.json(
        { success: false, message: 'Contract not found' },
        { status: 404 }
      );
    }

    // Delete the contract
    await prisma.contract.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Contract deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting contract:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete contract' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware to the handlers
// Allow VISITORS to view contracts they created
const protectedGET = withAuth(GET, ['ADMIN', 'RESIDENT', 'VISITORS']);
// Allow RESIDENTS to update their own contracts (for demo purposes)
const protectedPUT = withAuth(PUT, ['ADMIN', 'RESIDENT', 'VISITORS']);
const protectedDELETE = withAuth(DELETE, ['ADMIN']);

export { protectedGET as GET, protectedPUT as PUT, protectedDELETE as DELETE };
