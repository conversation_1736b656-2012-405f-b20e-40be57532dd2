"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';

// This is a redirect page to handle the old contract route
export default function ContractRedirectPage({ params }: { params: { roomId: string } }) {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();

  useEffect(() => {
    // If user is not logged in, redirect to login page
    if (!authLoading && !user) {
      router.push('/auth?redirect=' + encodeURIComponent(`/contract/room/${params.roomId}`));
      return;
    }

    // If user is logged in, redirect to the new contract/room route
    if (!authLoading && user) {
      router.replace(`/contract/room/${params.roomId}`);
    }
  }, [params.roomId, user, authLoading, router]);

  // Loading state while redirecting
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>
  );
}
