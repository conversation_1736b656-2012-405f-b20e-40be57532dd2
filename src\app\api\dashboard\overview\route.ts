import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/auth';
import prisma from '@/lib/prisma';

async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from the request
    const user = (request as any).user;

    // Verify user exists
    if (!user || !user.id) {
      return NextResponse.json(
        { success: false, message: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get user data with related information
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        rooms: {
          include: {
            roomType: true
          }
        }
      }
    });

    if (!userData) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Get active contract for the user
    const contract = await prisma.contract.findFirst({
      where: {
        residentId: user.id,
        isActive: true
      },
      orderBy: {
        startDate: 'desc'
      },
      include: {
        room: {
          include: {
            roomType: true
          }
        }
      }
    });

    // Get recent payments
    const payments = await prisma.payment.findMany({
      where: {
        userId: user.id
      },
      orderBy: {
        dueDate: 'desc'
      },
      take: 5
    });

    // Get next payment due
    const nextPayment = await prisma.payment.findFirst({
      where: {
        userId: user.id,
        status: 'PENDING'
      },
      orderBy: {
        dueDate: 'asc'
      }
    });

    // Get recent service requests
    const serviceRequests = await prisma.serviceRequest.findMany({
      where: {
        userId: user.id
      },
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        responses: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 1
        }
      },
      take: 3
    });

    // Get latest utility readings
    let utilityReadings = null;
    if (userData.rooms && userData.rooms.length > 0) {
      const roomId = userData.rooms[0].id;
      
      // Get the two most recent utility readings for the room
      const readings = await prisma.utilityReading.findMany({
        where: {
          roomId: roomId
        },
        orderBy: {
          readingDate: 'desc'
        },
        take: 2
      });

      if (readings.length >= 2) {
        // Current is the most recent, previous is the second most recent
        const current = readings[0];
        const previous = readings[1];

        // Calculate water and electricity usage
        const waterUsage = current.waterReading - previous.waterReading;
        const electricityUsage = current.electricityReading - previous.electricityReading;

        // Assume rates (these could be stored in a settings table in a real app)
        const waterRate = 18; // baht per cubic meter
        const electricityRate = 4; // baht per kWh

        utilityReadings = {
          water: {
            current: current.waterReading,
            previous: previous.waterReading,
            usage: waterUsage,
            rate: waterRate
          },
          electricity: {
            current: current.electricityReading,
            previous: previous.electricityReading,
            usage: electricityUsage,
            rate: electricityRate
          }
        };
      }
    }

    // Construct the response object
    const dashboardData = {
      user: {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        role: userData.role
      },
      room: userData.rooms.length > 0 ? {
        id: userData.rooms[0].id,
        roomNumber: userData.rooms[0].roomNumber,
        roomType: userData.rooms[0].roomType.name
      } : null,
      contract: contract ? {
        id: contract.id,
        startDate: contract.startDate,
        endDate: contract.endDate,
        rentAmount: contract.rentAmount,
        depositAmount: contract.depositAmount
      } : null,
      nextPayment: nextPayment ? {
        id: nextPayment.id,
        amount: nextPayment.amount,
        dueDate: nextPayment.dueDate,
        status: nextPayment.status,
        type: nextPayment.type
      } : null,
      payments: payments.map(payment => ({
        id: payment.id,
        amount: payment.amount,
        date: payment.dueDate,
        paidDate: payment.paidDate,
        status: payment.status,
        type: payment.type
      })),
      serviceRequests: serviceRequests.map(request => ({
        id: request.id,
        type: request.type,
        title: request.title,
        description: request.description,
        status: request.status,
        priority: request.priority,
        createdAt: request.createdAt,
        updatedAt: request.updatedAt,
        adminResponse: request.responses.length > 0 ? request.responses[0].message : null
      })),
      utilities: utilityReadings
    };

    return NextResponse.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}

// Apply authentication middleware
const protectedGET = withAuth(GET, ['ADMIN', 'RESIDENT', 'VISITORS']);

export { protectedGET as GET };
