"use client";

import { ReactNode } from 'react';
import MainFooter from './MainFooter';
import CompactFooter from './CompactFooter';
import ThaiFooter from './ThaiFooter';

type FooterType = 'main' | 'compact' | 'thai';

interface FooterSelectorProps {
  type?: FooterType;
  language?: 'en' | 'th';
  children?: ReactNode;
}

export default function FooterSelector({ 
  type = 'main', 
  language = 'en',
  children 
}: FooterSelectorProps) {
  // If children are provided, render them instead of a predefined footer
  if (children) {
    return <>{children}</>;
  }
  
  // If language is Thai, use the Thai footer regardless of type
  if (language === 'th') {
    return <ThaiFooter />;
  }
  
  // Otherwise, select the footer based on type
  switch (type) {
    case 'compact':
      return <CompactFooter />;
    case 'thai':
      return <ThaiFooter />;
    case 'main':
    default:
      return <MainFooter />;
  }
}
