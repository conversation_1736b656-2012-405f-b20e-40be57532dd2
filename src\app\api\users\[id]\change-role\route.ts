import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// POST change a user's role
async function handlePost(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { role } = body;

    // Validate required fields
    if (!role) {
      return NextResponse.json(
        { success: false, message: 'Role is required' },
        { status: 400 }
      );
    }

    // Validate role
    const validRoles = ['ADMIN', 'RESIDENT', 'VISITORS'];
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { success: false, message: 'Invalid role' },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        rooms: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Special validation for RESIDENT role
    if (role === 'RESIDENT' && user.rooms.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Cannot set role to RESIDENT because user has no assigned rooms. Please assign a room first.' 
        },
        { status: 400 }
      );
    }

    // Update the user's role
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        role: role as any
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        updatedAt: true
      }
    });

    return NextResponse.json({
      success: true,
      message: 'User role updated successfully',
      data: updatedUser
    });
  } catch (error) {
    console.error(`Error changing role for user with ID ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to change user role' },
      { status: 500 }
    );
  }
}

// Export the handler with authentication middleware
export const POST = withAuth(handlePost, ['ADMIN']);
