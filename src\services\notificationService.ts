// Notification service for handling user notifications

/**
 * Get all notifications for the current user
 * @param limit Optional limit for the number of notifications to return
 * @returns Promise with the API response
 */
export async function getUserNotifications(limit?: number) {
  try {
    const url = limit ? `/api/notifications?limit=${limit}` : '/api/notifications';
    
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    return response.json();
  } catch (error) {
    console.error('Error fetching notifications:', error);
    throw error;
  }
}

/**
 * Mark a notification as read
 * @param id Notification ID
 * @returns Promise with the API response
 */
export async function markNotificationAsRead(id: string) {
  try {
    const response = await fetch(`/api/notifications/${id}/read`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    return response.json();
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
}

/**
 * Mark all notifications as read
 * @returns Promise with the API response
 */
export async function markAllNotificationsAsRead() {
  try {
    const response = await fetch('/api/notifications/read-all', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    return response.json();
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
}

/**
 * Create a payment due notification for a user
 * @param userId User ID
 * @param amount Payment amount
 * @param dueDate Payment due date
 * @param paymentType Payment type
 * @returns Promise with the API response
 */
export async function createPaymentDueNotification(
  userId: string,
  amount: number,
  dueDate: string,
  paymentType: string
) {
  try {
    const response = await fetch('/api/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        userId,
        title: 'Payment Due',
        message: `Your ${paymentType.toLowerCase()} payment of ฿${amount} is due on ${new Date(dueDate).toLocaleDateString()}.`,
        type: 'PAYMENT_DUE',
        link: '/user/dashboard/payments'
      })
    });
    
    return response.json();
  } catch (error) {
    console.error('Error creating payment due notification:', error);
    throw error;
  }
}

/**
 * Create a payment received notification for a user
 * @param userId User ID
 * @param amount Payment amount
 * @param paymentType Payment type
 * @returns Promise with the API response
 */
export async function createPaymentReceivedNotification(
  userId: string,
  amount: number,
  paymentType: string
) {
  try {
    const response = await fetch('/api/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        userId,
        title: 'Payment Received',
        message: `Your ${paymentType.toLowerCase()} payment of ฿${amount} has been received.`,
        type: 'PAYMENT_RECEIVED',
        link: '/user/dashboard/payments'
      })
    });
    
    return response.json();
  } catch (error) {
    console.error('Error creating payment received notification:', error);
    throw error;
  }
}

/**
 * Send payment reminders to users with upcoming or overdue payments
 * @param paymentIds Array of payment IDs to send reminders for
 * @returns Promise with the API response
 */
export async function sendPaymentReminders(paymentIds: string[]) {
  try {
    const response = await fetch('/api/admin/payments/send-reminders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        paymentIds
      })
    });
    
    return response.json();
  } catch (error) {
    console.error('Error sending payment reminders:', error);
    throw error;
  }
}

/**
 * Create a system notification for all users or specific roles
 * @param title Notification title
 * @param message Notification message
 * @param link Optional link to navigate to when clicked
 * @param roles Optional array of user roles to target
 * @returns Promise with the API response
 */
export async function createSystemNotification(
  title: string,
  message: string,
  link?: string,
  roles?: string[]
) {
  try {
    const response = await fetch('/api/admin/notifications/system', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        title,
        message,
        link,
        roles
      })
    });
    
    return response.json();
  } catch (error) {
    console.error('Error creating system notification:', error);
    throw error;
  }
}
