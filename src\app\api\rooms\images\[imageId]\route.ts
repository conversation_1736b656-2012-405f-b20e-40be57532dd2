import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';

// DELETE a room image
async function handleDelete(
  request: NextRequest,
  { params }: { params: { imageId: string } }
) {
  try {
    const { imageId } = params;

    // Check if image exists
    const image = await prisma.roomImage.findUnique({
      where: { id: imageId }
    });

    if (!image) {
      return NextResponse.json(
        { success: false, message: 'Image not found' },
        { status: 404 }
      );
    }

    // Delete the image
    await prisma.roomImage.delete({
      where: { id: imageId }
    });

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting image with ID ${params.imageId}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete image' },
      { status: 500 }
    );
  }
}

// Export the handlers with authentication middleware
export const DELETE = withAuth(handleDelete, ['ADMIN']);
